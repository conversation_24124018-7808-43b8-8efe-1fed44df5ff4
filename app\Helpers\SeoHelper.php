<?php

namespace App\Helpers;

use App\Models\Page;
use Illuminate\Support\Facades\View;

class SeoHelper
{
    /**
     * Generate meta tags for a page.
     */
    public static function generateMetaTags(Page $page): array
    {
        $metaTags = [];

        // Basic meta tags
        $metaTags['title'] = $page->meta_title ?: $page->title;
        $metaTags['description'] = $page->meta_description ?: $page->excerpt;
        
        if ($page->meta_keywords) {
            $metaTags['keywords'] = implode(', ', $page->meta_keywords);
        }

        // Open Graph tags
        $seoData = $page->seo_data ?: [];
        
        $metaTags['og:title'] = $seoData['og_title'] ?? $metaTags['title'];
        $metaTags['og:description'] = $seoData['og_description'] ?? $metaTags['description'];
        $metaTags['og:type'] = $seoData['og_type'] ?? 'article';
        $metaTags['og:url'] = request()->url();
        $metaTags['og:site_name'] = 'Kanha Fashion Hub';
        
        if (isset($seoData['og_image'])) {
            $metaTags['og:image'] = $seoData['og_image'];
        } elseif ($page->featured_image) {
            $metaTags['og:image'] = $page->featured_image;
        } else {
            $metaTags['og:image'] = asset('images/logo-og.png');
        }

        // Twitter Card tags
        $metaTags['twitter:card'] = $seoData['twitter_card'] ?? 'summary_large_image';
        $metaTags['twitter:title'] = $seoData['twitter_title'] ?? $metaTags['title'];
        $metaTags['twitter:description'] = $seoData['twitter_description'] ?? $metaTags['description'];
        $metaTags['twitter:image'] = $seoData['twitter_image'] ?? $metaTags['og:image'];
        
        if (isset($seoData['twitter_site'])) {
            $metaTags['twitter:site'] = $seoData['twitter_site'];
        }

        // Additional meta tags
        $metaTags['robots'] = $seoData['robots'] ?? 'index, follow';
        $metaTags['canonical'] = $seoData['canonical'] ?? request()->url();

        return $metaTags;
    }

    /**
     * Generate structured data for a page.
     */
    public static function generateStructuredData(Page $page): array
    {
        $structuredData = [];

        // Breadcrumb structured data
        $structuredData['breadcrumb'] = [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => [
                [
                    '@type' => 'ListItem',
                    'position' => 1,
                    'name' => 'Home',
                    'item' => route('home')
                ],
                [
                    '@type' => 'ListItem',
                    'position' => 2,
                    'name' => $page->title,
                    'item' => request()->url()
                ]
            ]
        ];

        // Article structured data
        if ($page->template !== 'contact') {
            $structuredData['article'] = [
                '@context' => 'https://schema.org',
                '@type' => 'Article',
                'headline' => $page->title,
                'description' => $page->meta_description ?: $page->excerpt,
                'author' => [
                    '@type' => 'Organization',
                    'name' => 'Kanha Fashion Hub'
                ],
                'publisher' => [
                    '@type' => 'Organization',
                    'name' => 'Kanha Fashion Hub',
                    'logo' => [
                        '@type' => 'ImageObject',
                        'url' => asset('images/logo.png')
                    ]
                ],
                'datePublished' => $page->published_at ? $page->published_at->toISOString() : $page->created_at->toISOString(),
                'dateModified' => $page->updated_at->toISOString(),
                'mainEntityOfPage' => [
                    '@type' => 'WebPage',
                    '@id' => request()->url()
                ]
            ];

            if ($page->featured_image) {
                $structuredData['article']['image'] = $page->featured_image;
            }
        }

        // Organization structured data for contact page
        if ($page->template === 'contact') {
            $structuredData['organization'] = [
                '@context' => 'https://schema.org',
                '@type' => 'JewelryStore',
                'name' => 'Kanha Fashion Hub',
                'description' => 'Premium jewelry store offering exquisite gold, silver, and diamond jewelry',
                'url' => route('home'),
                'logo' => asset('images/logo.png'),
                'address' => [
                    '@type' => 'PostalAddress',
                    'streetAddress' => '123 Jewelry Street',
                    'addressLocality' => 'Mumbai',
                    'addressRegion' => 'Maharashtra',
                    'postalCode' => '400001',
                    'addressCountry' => 'IN'
                ],
                'contactPoint' => [
                    '@type' => 'ContactPoint',
                    'telephone' => '+91-XXXX-XXXXXX',
                    'contactType' => 'customer service',
                    'availableLanguage' => ['English', 'Hindi']
                ],
                'openingHours' => [
                    'Mo-Sa 10:00-20:00',
                    'Su 11:00-18:00'
                ],
                'priceRange' => '₹₹₹'
            ];
        }

        return $structuredData;
    }

    /**
     * Generate sitemap data for pages.
     */
    public static function generateSitemapData(): array
    {
        $pages = Page::published()
                    ->select(['slug', 'updated_at', 'published_at', 'template'])
                    ->get();

        $sitemapData = [];

        foreach ($pages as $page) {
            $priority = '0.7';
            $changefreq = 'monthly';

            // Adjust priority based on page type
            switch ($page->template) {
                case 'about':
                case 'contact':
                    $priority = '0.8';
                    $changefreq = 'yearly';
                    break;
                case 'legal':
                case 'policy':
                    $priority = '0.5';
                    $changefreq = 'yearly';
                    break;
                case 'guide':
                    $priority = '0.6';
                    $changefreq = 'monthly';
                    break;
            }

            $sitemapData[] = [
                'url' => route('page.show', $page->slug),
                'lastmod' => $page->updated_at->format('Y-m-d'),
                'changefreq' => $changefreq,
                'priority' => $priority
            ];
        }

        return $sitemapData;
    }

    /**
     * Render meta tags as HTML.
     */
    public static function renderMetaTags(array $metaTags): string
    {
        $html = '';

        foreach ($metaTags as $name => $content) {
            if (empty($content)) {
                continue;
            }

            if (str_starts_with($name, 'og:') || str_starts_with($name, 'twitter:')) {
                $html .= '<meta property="' . $name . '" content="' . htmlspecialchars($content) . '">' . "\n";
            } elseif ($name === 'canonical') {
                $html .= '<link rel="canonical" href="' . htmlspecialchars($content) . '">' . "\n";
            } elseif ($name === 'title') {
                // Title is handled separately
                continue;
            } else {
                $html .= '<meta name="' . $name . '" content="' . htmlspecialchars($content) . '">' . "\n";
            }
        }

        return $html;
    }

    /**
     * Render structured data as JSON-LD.
     */
    public static function renderStructuredData(array $structuredData): string
    {
        $html = '';

        foreach ($structuredData as $data) {
            $html .= '<script type="application/ld+json">' . "\n";
            $html .= json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n";
            $html .= '</script>' . "\n";
        }

        return $html;
    }

    /**
     * Get SEO score for a page.
     */
    public static function getSeoScore(Page $page): array
    {
        $score = 0;
        $maxScore = 100;
        $issues = [];
        $recommendations = [];

        // Title check (20 points)
        if (!empty($page->meta_title)) {
            $titleLength = strlen($page->meta_title);
            if ($titleLength >= 30 && $titleLength <= 60) {
                $score += 20;
            } elseif ($titleLength > 0) {
                $score += 10;
                if ($titleLength < 30) {
                    $issues[] = 'Meta title is too short (less than 30 characters)';
                    $recommendations[] = 'Expand your meta title to 30-60 characters for better SEO';
                } else {
                    $issues[] = 'Meta title is too long (more than 60 characters)';
                    $recommendations[] = 'Shorten your meta title to 30-60 characters';
                }
            }
        } else {
            $issues[] = 'Missing meta title';
            $recommendations[] = 'Add a unique meta title for this page';
        }

        // Description check (20 points)
        if (!empty($page->meta_description)) {
            $descLength = strlen($page->meta_description);
            if ($descLength >= 120 && $descLength <= 160) {
                $score += 20;
            } elseif ($descLength > 0) {
                $score += 10;
                if ($descLength < 120) {
                    $issues[] = 'Meta description is too short (less than 120 characters)';
                    $recommendations[] = 'Expand your meta description to 120-160 characters';
                } else {
                    $issues[] = 'Meta description is too long (more than 160 characters)';
                    $recommendations[] = 'Shorten your meta description to 120-160 characters';
                }
            }
        } else {
            $issues[] = 'Missing meta description';
            $recommendations[] = 'Add a compelling meta description for this page';
        }

        // Content length check (15 points)
        $contentLength = str_word_count(strip_tags($page->content));
        if ($contentLength >= 300) {
            $score += 15;
        } elseif ($contentLength >= 150) {
            $score += 10;
            $issues[] = 'Content is relatively short (' . $contentLength . ' words)';
            $recommendations[] = 'Consider adding more valuable content (aim for 300+ words)';
        } else {
            $score += 5;
            $issues[] = 'Content is too short (' . $contentLength . ' words)';
            $recommendations[] = 'Add more comprehensive content (aim for 300+ words)';
        }

        // Keywords check (15 points)
        if (!empty($page->meta_keywords) && count($page->meta_keywords) > 0) {
            $keywordCount = count($page->meta_keywords);
            if ($keywordCount >= 3 && $keywordCount <= 10) {
                $score += 15;
            } elseif ($keywordCount > 0) {
                $score += 10;
                if ($keywordCount < 3) {
                    $issues[] = 'Too few keywords (' . $keywordCount . ')';
                    $recommendations[] = 'Add 3-10 relevant keywords for better targeting';
                } else {
                    $issues[] = 'Too many keywords (' . $keywordCount . ')';
                    $recommendations[] = 'Focus on 3-10 most relevant keywords';
                }
            }
        } else {
            $issues[] = 'No keywords specified';
            $recommendations[] = 'Add 3-10 relevant keywords for this page';
        }

        // Featured image check (10 points)
        if (!empty($page->featured_image)) {
            $score += 10;
        } else {
            $issues[] = 'No featured image';
            $recommendations[] = 'Add a relevant featured image to improve social sharing';
        }

        // Slug check (10 points)
        if (!empty($page->slug)) {
            $slugLength = strlen($page->slug);
            if ($slugLength <= 60 && !str_contains($page->slug, '_')) {
                $score += 10;
            } else {
                $score += 5;
                if ($slugLength > 60) {
                    $issues[] = 'URL slug is too long';
                    $recommendations[] = 'Shorten the URL slug for better readability';
                }
                if (str_contains($page->slug, '_')) {
                    $issues[] = 'URL slug contains underscores';
                    $recommendations[] = 'Use hyphens instead of underscores in URL slug';
                }
            }
        }

        // Excerpt check (10 points)
        if (!empty($page->excerpt)) {
            $excerptLength = strlen($page->excerpt);
            if ($excerptLength >= 100 && $excerptLength <= 200) {
                $score += 10;
            } elseif ($excerptLength > 0) {
                $score += 5;
                if ($excerptLength < 100) {
                    $issues[] = 'Excerpt is too short';
                    $recommendations[] = 'Expand the excerpt to 100-200 characters';
                } else {
                    $issues[] = 'Excerpt is too long';
                    $recommendations[] = 'Shorten the excerpt to 100-200 characters';
                }
            }
        } else {
            $issues[] = 'No excerpt provided';
            $recommendations[] = 'Add a compelling excerpt for this page';
        }

        return [
            'score' => $score,
            'percentage' => round(($score / $maxScore) * 100),
            'grade' => self::getGrade($score, $maxScore),
            'issues' => $issues,
            'recommendations' => $recommendations
        ];
    }

    /**
     * Get SEO grade based on score.
     */
    private static function getGrade(int $score, int $maxScore): string
    {
        $percentage = ($score / $maxScore) * 100;

        if ($percentage >= 90) {
            return 'A+';
        } elseif ($percentage >= 80) {
            return 'A';
        } elseif ($percentage >= 70) {
            return 'B';
        } elseif ($percentage >= 60) {
            return 'C';
        } elseif ($percentage >= 50) {
            return 'D';
        } else {
            return 'F';
        }
    }
}
