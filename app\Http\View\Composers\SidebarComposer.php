<?php

namespace App\Http\View\Composers;

use App\Models\Page;
use Illuminate\View\View;

class SidebarComposer
{
    /**
     * Bind data to the view.
     */
    public function compose(View $view): void
    {
        $menuPages = Page::published()
                        ->forMenu()
                        ->select(['title', 'slug', 'menu_order'])
                        ->get();

        $footerPages = Page::published()
                          ->forFooter()
                          ->select(['title', 'slug', 'template'])
                          ->get();

        $view->with([
            'menuPages' => $menuPages,
            'footerPages' => $footerPages,
        ]);
    }
}
