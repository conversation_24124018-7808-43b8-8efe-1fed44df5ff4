@extends('layouts.app')

@section('title', 'Order Confirmation - Kanha Fashion Hub')
@section('description', 'Your order has been successfully placed. Thank you for shopping with Kanha Fashion Hub.')

@section('content')
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Success Message -->
                <div class="text-center mb-5">
                    <div class="success-icon mb-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                    </div>
                    <h1 class="font-playfair text-success mb-3">Order Confirmed!</h1>
                    <p class="lead text-muted">Thank you for your purchase. Your order has been successfully placed.</p>
                    <div class="alert alert-success d-inline-block">
                        <strong>Order Number:</strong> {{ $order->order_number }}
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Order Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Order Details</h6>
                                <p class="mb-1"><strong>Order Number:</strong> {{ $order->order_number }}</p>
                                <p class="mb-1"><strong>Order Date:</strong> {{ $order->created_at->format('M d, Y H:i A') }}</p>
                                <p class="mb-1"><strong>Payment Method:</strong> {{ ucfirst($order->payment_method) }}</p>
                                <p class="mb-1"><strong>Payment Status:</strong> 
                                    <span class="badge bg-success">{{ ucfirst($order->payment_status) }}</span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6>Shipping Information</h6>
                                @if($order->shipping_address && is_array($order->shipping_address))
                                    <p class="mb-1"><strong>Name:</strong> {{ $order->billing_address['name'] ?? $order->user->name ?? 'N/A' }}</p>
                                    <p class="mb-1"><strong>Address:</strong> {{ $order->shipping_address['address'] ?? 'N/A' }}</p>
                                    <p class="mb-1"><strong>City:</strong> {{ ($order->shipping_address['city'] ?? 'N/A') . ', ' . ucfirst($order->shipping_address['state'] ?? 'N/A') }}</p>
                                    <p class="mb-1"><strong>Pincode:</strong> {{ $order->shipping_address['pincode'] ?? 'N/A' }}</p>
                                    @if(isset($order->shipping_address['country']))
                                        <p class="mb-1"><strong>Country:</strong> {{ ucfirst($order->shipping_address['country']) }}</p>
                                    @endif
                                @else
                                    <p class="text-muted">Shipping address not available</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Items -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Items Ordered</h5>
                    </div>
                    <div class="card-body">
                        @foreach($order->orderItems as $item)
                        <div class="d-flex align-items-center py-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                            @if($item->product && $item->product->images && count($item->product->images) > 0)
                                <img src="{{ $item->product->images[0] }}" alt="{{ $item->product_name }}" 
                                     class="me-3" style="width: 80px; height: 80px; object-fit: cover; border-radius: 8px;">
                            @else
                                <div class="bg-light me-3 d-flex align-items-center justify-content-center" 
                                     style="width: 80px; height: 80px; border-radius: 8px;">
                                    <i class="fas fa-gem text-muted"></i>
                                </div>
                            @endif
                            
                            <div class="flex-fill">
                                <h6 class="mb-1">{{ $item->product_name }}</h6>
                                <p class="text-muted mb-1">SKU: {{ $item->product_sku }}</p>
                                @if($item->size)
                                <p class="text-muted mb-1">Size: {{ $item->size }}</p>
                                @endif
                                <p class="text-muted mb-0">Quantity: {{ $item->quantity }}</p>
                            </div>
                            
                            <div class="text-end">
                                <p class="mb-0 fw-bold">₹{{ number_format($item->total, 2) }}</p>
                                <small class="text-muted">₹{{ number_format($item->price, 2) }} each</small>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>

                <!-- Order Total -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 offset-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td>Subtotal:</td>
                                        <td class="text-end">₹{{ number_format($order->subtotal ?? 0, 2) }}</td>
                                    </tr>

                                    <tr>
                                        <td>Shipping:</td>
                                        <td class="text-end">₹{{ number_format($order->shipping_amount ?? 0, 2) }}</td>
                                    </tr>
                                    @if(($order->discount_amount ?? 0) > 0)
                                    <tr>
                                        <td>Discount:</td>
                                        <td class="text-end text-success">-₹{{ number_format($order->discount_amount, 2) }}</td>
                                    </tr>
                                    @endif
                                    <tr class="border-top">
                                        <td><strong>Total:</strong></td>
                                        <td class="text-end"><strong>₹{{ number_format($order->total_amount ?? 0, 2) }}</strong></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- What's Next -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">What's Next?</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="fas fa-envelope text-primary-pink mb-2" style="font-size: 2rem;"></i>
                                    <h6>Order Confirmation</h6>
                                    <p class="text-muted small">You'll receive an email confirmation shortly with your order details.</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="fas fa-box text-primary-pink mb-2" style="font-size: 2rem;"></i>
                                    <h6>Processing</h6>
                                    <p class="text-muted small">We'll start processing your order within 24 hours.</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="fas fa-shipping-fast text-primary-pink mb-2" style="font-size: 2rem;"></i>
                                    <h6>Shipping</h6>
                                    <p class="text-muted small">You'll receive tracking information once your order ships.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="text-center">
                    <a href="{{ route('orders') }}" class="btn btn-primary-pink me-3">
                        <i class="fas fa-list me-2"></i>View All Orders
                    </a>
                    <a href="{{ route('home') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-home me-2"></i>Continue Shopping
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Order Tracking Modal -->
<div class="modal fade" id="trackingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Track Your Order</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Your order <strong>{{ $order->order_number }}</strong> is currently being processed.</p>
                <p>You can track your order status anytime by:</p>
                <ul>
                    <li>Visiting your <a href="{{ route('orders') }}">Orders page</a></li>
                    <li>Using the order number: <strong>{{ $order->order_number }}</strong></li>
                </ul>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    You'll receive SMS and email updates as your order progresses.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="{{ route('orders') }}" class="btn btn-primary-pink">View Orders</a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Auto-show tracking modal after 3 seconds
    setTimeout(function() {
        var trackingModal = new bootstrap.Modal(document.getElementById('trackingModal'));
        trackingModal.show();
    }, 3000);

    // Confetti animation (optional)
    function createConfetti() {
        const colors = ['#e91e63', '#ffd700', '#ff69b4', '#ff1493'];
        for (let i = 0; i < 50; i++) {
            const confetti = document.createElement('div');
            confetti.style.position = 'fixed';
            confetti.style.left = Math.random() * 100 + 'vw';
            confetti.style.top = '-10px';
            confetti.style.width = '10px';
            confetti.style.height = '10px';
            confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.borderRadius = '50%';
            confetti.style.pointerEvents = 'none';
            confetti.style.zIndex = '9999';
            confetti.style.animation = `fall ${Math.random() * 3 + 2}s linear forwards`;
            
            document.body.appendChild(confetti);
            
            setTimeout(() => {
                confetti.remove();
            }, 5000);
        }
    }

    // Add CSS for confetti animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fall {
            to {
                transform: translateY(100vh) rotate(360deg);
            }
        }
    `;
    document.head.appendChild(style);

    // Trigger confetti on page load
    createConfetti();
</script>
@endpush
