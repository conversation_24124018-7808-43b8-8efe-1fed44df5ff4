@extends('layouts.admin')

@section('title', 'Size Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Size Management</h1>
                    <p class="text-muted">Manage sizes for different product categories</p>
                </div>
                <a href="{{ route('admin.sizes.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add New Size
                </a>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    @foreach($errors->all() as $error)
                        {{ $error }}<br>
                    @endforeach
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="card">
                <div class="card-body">
                    @if($sizes->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Name</th>
                                        <th>Display Name</th>
                                        <th>Category Type</th>
                                        <th>Sort Order</th>
                                        <th>Status</th>
                                        <th>Used in Categories</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sizes as $size)
                                        <tr>
                                            <td>
                                                <strong>{{ $size->name }}</strong>
                                            </td>
                                            <td>{{ $size->display_name }}</td>
                                            <td>
                                                <span class="badge bg-{{ $size->category_type === 'clothing' ? 'primary' : ($size->category_type === 'shoes' ? 'success' : ($size->category_type === 'jewelry' ? 'warning' : 'secondary')) }}">
                                                    {{ ucfirst($size->category_type) }}
                                                </span>
                                            </td>
                                            <td>{{ $size->sort_order }}</td>
                                            <td>
                                                @if($size->is_active)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-danger">Inactive</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($size->categories->count() > 0)
                                                    <small class="text-muted">
                                                        {{ $size->categories->count() }} {{ Str::plural('category', $size->categories->count()) }}
                                                    </small>
                                                @else
                                                    <small class="text-muted">Not assigned</small>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.sizes.edit', $size->id) }}" 
                                                       class="btn btn-sm btn-outline-primary" 
                                                       title="Edit Size">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete({{ $size->id }}, '{{ $size->display_name }}')"
                                                            title="Delete Size">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            {{ $sizes->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-ruler fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No sizes found</h5>
                            <p class="text-muted">Start by creating your first size.</p>
                            <a href="{{ route('admin.sizes.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add New Size
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the size "<span id="sizeName"></span>"?</p>
                <p class="text-danger"><small><i class="fas fa-exclamation-triangle me-1"></i>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete Size</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function confirmDelete(sizeId, sizeName) {
    document.getElementById('sizeName').textContent = sizeName;
    document.getElementById('deleteForm').action = `/admin/sizes/${sizeId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endpush
