# Hierarchical Category-Size System

## Overview

This document describes the new hierarchical category and size management system implemented for the Laravel e-commerce application. The system provides flexible category hierarchies with inherited size management and product-specific stock tracking.

## Features

### 🏗️ Hierarchical Categories
- **Parent-Child Relationships**: Categories can have parent categories (e.g., Shoes → Nike → Air Max)
- **Unlimited Depth**: Support for multiple levels of category nesting
- **Automatic Path Generation**: Materialized path for efficient hierarchy queries
- **Breadcrumb Navigation**: Automatic breadcrumb generation for category pages

### 📏 Size Management
- **Category-Specific Sizes**: Each category can define its own available sizes
- **Size Inheritance**: Child categories can inherit parent sizes or override them
- **Size Types**: Support for different size types (clothing, rings, shoes, jewelry, general)
- **Flexible Size System**: Easy to add new sizes and size types

### 📦 Product-Size-Stock Management
- **Size-Specific Stock**: Track stock quantity for each product-size combination
- **Price Adjustments**: Set price adjustments for specific sizes
- **SKU Suffixes**: Automatic SKU generation with size suffixes
- **Stock Status**: Real-time stock status calculation (in_stock, low_stock, out_of_stock)

### 🛒 Enhanced Shopping Experience
- **Dynamic Size Selection**: Size options load based on selected category
- **Stock Validation**: Real-time stock checking before adding to cart
- **Price Updates**: Dynamic price updates based on selected size
- **Visual Stock Indicators**: Clear indication of stock status for each size

## Database Schema

### New Tables

#### `sizes`
- `id` - Primary key
- `name` - Size name (e.g., "S", "6", "One Size")
- `display_name` - Display name (e.g., "Small", "Size 6")
- `category_type` - Size category (clothing, rings, shoes, jewelry, general)
- `sort_order` - Display order

#### `product_sizes`
- `id` - Primary key
- `product_id` - Foreign key to products
- `size_id` - Foreign key to sizes
- `stock_quantity` - Stock for this product-size combination
- `price_adjustment` - Price adjustment for this size
- `sku_suffix` - SKU suffix for this size
- `is_available` - Availability flag

#### `category_sizes`
- `category_id` - Foreign key to categories
- `size_id` - Foreign key to sizes

### Modified Tables

#### `categories`
- `parent_id` - Foreign key to parent category
- `level` - Hierarchy level (0 for root categories)
- `path` - Materialized path (e.g., "/1/5/12/")
- `inherit_parent_sizes` - Whether to inherit parent sizes

#### `cart_items` & `order_items`
- `size_id` - Foreign key to sizes
- `size_name` - Size name for historical records

## Models and Relationships

### Category Model
```php
// Hierarchy relationships
public function parent()
public function children()
public function ancestors()
public function descendants()

// Size relationships
public function sizes()
public function getAvailableSizes()

// Product relationships
public function allProducts() // Includes products from subcategories
```

### Product Model
```php
// Size relationships
public function productSizes()
public function availableSizes()
public function inStockSizes()

// Helper methods
public function getAvailableSizesWithStock()
public function getProductSize($sizeId)
public function hasSufficientStock($sizeId, $quantity)
```

### Size Model
```php
// Static methods
public static function getForCategoryType($type)
public static function createDefaults()
```

### ProductSize Model
```php
// Stock management
public function decrementStock($quantity)
public function incrementStock($quantity)
public function hasSufficientStock($quantity)

// Attributes
public function getFullSkuAttribute()
public function getFinalPriceAttribute()
public function getStockStatusAttribute()
```

## API Endpoints

### Admin API
- `GET /admin/api/categories/hierarchy` - Get category hierarchy
- `GET /admin/api/categories/{id}/sizes` - Get category sizes
- `POST /admin/api/categories/{id}/sizes` - Update category sizes
- `GET /admin/api/sizes/{type?}` - Get sizes by type

### Public API
- `GET /api/products/{id}/sizes` - Get product sizes with stock info

## Frontend Components

### Admin Interface

#### Category Management
- Parent category selection dropdown
- Size inheritance checkbox
- Size selection with category type filtering
- Dynamic size loading based on parent category

#### Product Management
- Dynamic size-stock matrix based on selected category
- Price adjustment inputs for each size
- SKU suffix generation
- Stock quantity management per size

### Customer Interface

#### Product Detail Page
- Dynamic size selection based on product category
- Real-time stock status indicators
- Price updates based on selected size
- Stock validation before adding to cart

#### Category Navigation
- Hierarchical breadcrumbs
- Subcategory navigation
- Products from all subcategories included

## Migration Process

The system includes migrations to preserve existing data:

1. **Add hierarchy fields** to categories table
2. **Create sizes table** with default sizes
3. **Create product_sizes table** for stock management
4. **Update cart/order tables** for size tracking
5. **Create category_sizes table** for size associations
6. **Migrate existing product sizes** from JSON to relational model

## Usage Examples

### Creating a Category Hierarchy
```php
// Create root category
$jewelry = Category::create([
    'name' => 'Jewelry',
    'slug' => 'jewelry'
]);

// Create child category
$rings = Category::create([
    'name' => 'Rings',
    'slug' => 'rings',
    'parent_id' => $jewelry->id,
    'inherit_parent_sizes' => false
]);

// Attach sizes to category
$ringSizes = Size::where('category_type', 'rings')->get();
$rings->sizes()->attach($ringSizes->pluck('id'));
```

### Managing Product Sizes
```php
// Create product with sizes
$product = Product::create([...]);

// Add size-specific stock
ProductSize::create([
    'product_id' => $product->id,
    'size_id' => $sizeId,
    'stock_quantity' => 10,
    'price_adjustment' => 50.00,
    'sku_suffix' => '-L'
]);

// Check stock for specific size
$hasStock = $product->hasSufficientStock($sizeId, 2);
```

## Deployment

1. **Run deployment script**: `php deploy_hierarchical_system.php`
2. **Run test script**: `php test_hierarchical_system.php`
3. **Configure categories** in admin panel
4. **Update products** with size-stock information
5. **Test frontend** functionality

## Backward Compatibility

The system maintains backward compatibility:
- Old size fields are preserved during migration
- Existing cart and order data remains intact
- Products without specific sizes use "One Size" default
- Old size data is available in `size_old` fields during transition

## Performance Considerations

- **Materialized Path**: Efficient hierarchy queries using path field
- **Eager Loading**: Relationships are eager loaded to minimize queries
- **Caching**: Consider caching category hierarchies and size data
- **Indexing**: Database indexes on foreign keys and path fields

## Troubleshooting

### Common Issues
1. **Missing sizes**: Run `php artisan db:seed --class=SizeSeeder`
2. **Hierarchy not updating**: Call `updateLevel()` and `updatePath()` on categories
3. **Stock not updating**: Check ProductSize relationships and stock methods
4. **API errors**: Verify routes are registered and controllers exist

### Debug Commands
```bash
# Check migrations
php artisan migrate:status

# Clear caches
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Test models in tinker
php artisan tinker
>>> App\Models\Size::count()
>>> App\Models\Category::with('children')->get()
```

## Future Enhancements

- **Bulk Operations**: Bulk size and stock management
- **Import/Export**: CSV import/export for categories and sizes
- **Analytics**: Size popularity and stock analytics
- **Automation**: Automatic reorder points and stock alerts
- **Multi-variant**: Support for color-size combinations

---

For technical support or questions about this system, refer to the Laravel documentation and the model files for detailed method documentation.
