<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            $table->foreignId('parent_id')->nullable()->after('id')->constrained('categories')->onDelete('cascade');
            $table->json('available_sizes')->nullable()->after('description');
            $table->boolean('inherit_parent_sizes')->default(true)->after('available_sizes');
            $table->integer('level')->default(0)->after('inherit_parent_sizes');
            $table->string('path')->nullable()->after('level'); // For efficient hierarchy queries
            
            $table->index(['parent_id', 'is_active']);
            $table->index(['level', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            $table->dropForeign(['parent_id']);
            $table->dropIndex(['parent_id', 'is_active']);
            $table->dropIndex(['level', 'sort_order']);
            $table->dropColumn(['parent_id', 'available_sizes', 'inherit_parent_sizes', 'level', 'path']);
        });
    }
};
