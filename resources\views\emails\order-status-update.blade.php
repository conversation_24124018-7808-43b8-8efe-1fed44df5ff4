<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Status Update - {{ $order->order_number }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #8B4513;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #8B4513;
            margin-bottom: 10px;
        }
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 12px;
            margin: 10px 0;
        }
        .status-confirmed { background-color: #e3f2fd; color: #1976d2; }
        .status-processing { background-color: #fff3e0; color: #f57c00; }
        .status-shipped { background-color: #e8f5e8; color: #388e3c; }
        .status-delivered { background-color: #e8f5e8; color: #2e7d32; }
        .status-cancelled { background-color: #ffebee; color: #d32f2f; }
        .order-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .tracking-info {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #8B4513;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .order-items {
            margin: 20px 0;
        }
        .item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">{{ config('app.name', 'Kanha Fashion Hub') }}</div>
            <p>Exquisite Jewelry, Timeless Elegance</p>
        </div>

        <h2>Order Status Update</h2>
        
        <p>Dear {{ $customer->name ?? 'Valued Customer' }},</p>

        @php
            $statusMessages = [
                'confirmed' => 'Great news! Your order has been confirmed and is being prepared with care.',
                'processing' => 'Your beautiful jewelry is being crafted by our skilled artisans.',
                'shipped' => 'Exciting news! Your order has been shipped and is on its way to you.',
                'delivered' => 'Your order has been delivered! We hope you love your new jewelry.',
                'cancelled' => 'Your order has been cancelled. If you have any questions, please don\'t hesitate to contact us.',
            ];
        @endphp

        <p>{{ $statusMessages[$status] ?? 'Your order status has been updated.' }}</p>

        <div class="order-info">
            <h3>Order Details</h3>
            <p><strong>Order Number:</strong> {{ $order->order_number }}</p>
            <p><strong>Order Date:</strong> {{ $order->created_at->format('F j, Y') }}</p>
            <p><strong>Status:</strong> 
                <span class="status-badge status-{{ $status }}">{{ ucfirst($status) }}</span>
            </p>
            <p><strong>Total Amount:</strong> ₹{{ number_format($order->total, 2) }}</p>
        </div>

        @if($trackingNumber && $status === 'shipped')
            <div class="tracking-info">
                <h4>📦 Tracking Information</h4>
                <p><strong>Tracking Number:</strong> {{ $trackingNumber }}</p>
                <p>You can track your package using the tracking number above with our shipping partner.</p>
            </div>
        @endif

        @if($orderItems->count() > 0)
            <div class="order-items">
                <h4>Items in Your Order:</h4>
                @foreach($orderItems as $item)
                    <div class="item">
                        <div>
                            <strong>{{ $item->product_name }}</strong>
                            @if($item->size)
                                <br><small>Size: {{ $item->size }}</small>
                            @endif
                            <br><small>Quantity: {{ $item->quantity }}</small>
                        </div>
                        <div>₹{{ number_format($item->total, 2) }}</div>
                    </div>
                @endforeach
            </div>
        @endif

        @if($status === 'shipped')
            <p><strong>Estimated Delivery:</strong> 3-5 business days</p>
            <p>Please ensure someone is available to receive the package. A signature may be required for delivery.</p>
        @elseif($status === 'delivered')
            <p>Thank you for choosing {{ config('app.name') }}! We hope you love your new jewelry.</p>
            <p>If you're happy with your purchase, we'd love to hear from you. Consider leaving us a review!</p>
        @elseif($status === 'cancelled')
            <p>If this cancellation was unexpected or if you have any questions, please contact our customer service team.</p>
        @endif

        <div class="footer">
            <p>Need help? Contact us:</p>
            <p>📧 Email: {{ config('mail.from.address') }}</p>
            <p>📱 Phone: +91-XXXXXXXXXX</p>
            <p>🌐 Website: {{ config('app.url') }}</p>
            
            <p style="margin-top: 20px; font-size: 12px; color: #999;">
                This is an automated email regarding your order. Please do not reply to this email.
                <br>
                © {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
