<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $name
 * @property string $display_name
 * @property string $category_type
 * @property string $description
 * @property int $sort_order
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 *
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\ProductSize[] $productSizes
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Product[] $products
 *
 * @method static \Illuminate\Database\Eloquent\Builder active()
 * @method static \Illuminate\Database\Eloquent\Builder forCategoryType(string $categoryType)
 * @method static \Illuminate\Database\Eloquent\Builder ordered()
 */
class Size extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'category_type',
        'description',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Relationships
    public function productSizes()
    {
        return $this->hasMany(ProductSize::class);
    }

    public function products()
    {
        return $this->belongsToMany(Product::class, 'product_sizes')
                    ->withPivot(['stock_quantity', 'sku_suffix', 'price_adjustment', 'is_available'])
                    ->withTimestamps();
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'category_sizes')
                    ->withTimestamps();
    }

    public function cartItems()
    {
        return $this->hasMany(CartItem::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForCategoryType($query, $categoryType)
    {
        return $query->where('category_type', $categoryType);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    // Helper methods
    public function getFullDisplayNameAttribute()
    {
        return $this->display_name ?: $this->name;
    }

    /**
     * Get sizes suitable for a specific category type
     */
    public static function getForCategoryType($categoryType)
    {
        return static::active()
                    ->where(function($query) use ($categoryType) {
                        $query->where('category_type', $categoryType)
                              ->orWhereNull('category_type');
                    })
                    ->ordered()
                    ->get();
    }

    /**
     * Create default sizes for common categories
     */
    public static function createDefaults()
    {
        $defaultSizes = [
            // Clothing sizes
            ['name' => 'XS', 'display_name' => 'Extra Small', 'category_type' => 'clothing', 'sort_order' => 1],
            ['name' => 'S', 'display_name' => 'Small', 'category_type' => 'clothing', 'sort_order' => 2],
            ['name' => 'M', 'display_name' => 'Medium', 'category_type' => 'clothing', 'sort_order' => 3],
            ['name' => 'L', 'display_name' => 'Large', 'category_type' => 'clothing', 'sort_order' => 4],
            ['name' => 'XL', 'display_name' => 'Extra Large', 'category_type' => 'clothing', 'sort_order' => 5],
            ['name' => 'XXL', 'display_name' => '2X Large', 'category_type' => 'clothing', 'sort_order' => 6],
            
            // Ring sizes
            ['name' => '5', 'display_name' => 'Size 5', 'category_type' => 'rings', 'sort_order' => 1],
            ['name' => '6', 'display_name' => 'Size 6', 'category_type' => 'rings', 'sort_order' => 2],
            ['name' => '7', 'display_name' => 'Size 7', 'category_type' => 'rings', 'sort_order' => 3],
            ['name' => '8', 'display_name' => 'Size 8', 'category_type' => 'rings', 'sort_order' => 4],
            ['name' => '9', 'display_name' => 'Size 9', 'category_type' => 'rings', 'sort_order' => 5],
            ['name' => '10', 'display_name' => 'Size 10', 'category_type' => 'rings', 'sort_order' => 6],
            
            // General/One size
            ['name' => 'OS', 'display_name' => 'One Size', 'category_type' => 'general', 'sort_order' => 1],
        ];

        foreach ($defaultSizes as $size) {
            static::firstOrCreate(
                ['name' => $size['name'], 'category_type' => $size['category_type']],
                $size
            );
        }
    }
}
