<?php

namespace App\Http\Controllers;

use App\Models\CartItem;
use App\Models\Product;
use App\Models\ProductSize;
use App\Models\Size;
use App\Models\Promocode;
use App\Models\PromocodeUsage;
use App\Models\Wishlist;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class CartController extends Controller
{
    public function index()
    {
        $cartItems = $this->getCartItems();
        $cartCount = $this->getCartCount();

        // Calculate totals - simplified without discount feature
        $subtotal = 0;

        foreach ($cartItems as $item) {
            // Use the total_price attribute which includes size-based price adjustments
            $subtotal += $item->total_price;
        }

        // Calculate shipping using dynamic settings
        $freeShippingThreshold = \App\Models\Setting::get('free_shipping_threshold', 25000);
        $shippingCharge = \App\Models\Setting::get('shipping_charge', 500);
        $shipping = $subtotal >= $freeShippingThreshold ? 0 : $shippingCharge;

        // Calculate total (no tax/GST)
        $total = $subtotal + $shipping;

        // Set discount to 0 since we removed the discount feature
        $discount = 0;

        return view('cart.index', compact('cartItems', 'cartCount', 'subtotal', 'discount', 'shipping', 'total', 'freeShippingThreshold', 'shippingCharge'));
    }

    public function add(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'size_id' => 'nullable|exists:sizes,id',
            'size' => 'nullable|string', // Keep for backward compatibility
            'options' => 'nullable|array'
        ]);

        $product = Product::with('productSizes')->findOrFail($request->product_id);
        $sizeId = $request->size_id;

        // If no size_id provided but size string is provided, try to find the size
        if (!$sizeId && $request->size) {
            $size = Size::where('name', $request->size)->first();
            $sizeId = $size ? $size->id : null;
        }

        // Check stock for specific size or overall product
        if ($sizeId) {
            $productSize = $product->getProductSize($sizeId);
            if (!$productSize || !$productSize->hasSufficientStock($request->quantity)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Selected size is out of stock or insufficient quantity available.'
                ], 400);
            }
        } else {
            // Check overall product stock
            if (!$product->hasSufficientStock($request->quantity)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product is out of stock or insufficient quantity available.'
                ], 400);
            }
        }

        // Always use session-based cart for guest experience
        $sessionId = Session::getId();

        // Check if item already exists in cart
        $existingItem = CartItem::where('product_id', $request->product_id)
            ->where('session_id', $sessionId)
            ->where('size_id', $sizeId)
            ->first();

        if ($existingItem) {
            $newQuantity = $existingItem->quantity + $request->quantity;

            // Check stock again for new quantity
            if ($sizeId) {
                $productSize = $product->getProductSize($sizeId);
                if (!$productSize || !$productSize->hasSufficientStock($newQuantity)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot add more items. Insufficient stock available for selected size.'
                    ], 400);
                }
            } else {
                if (!$product->hasSufficientStock($newQuantity)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot add more items. Insufficient stock available.'
                    ], 400);
                }
            }

            $existingItem->update(['quantity' => $newQuantity]);
        } else {
            CartItem::create([
                'user_id' => null, // Always null for guest experience
                'session_id' => $sessionId,
                'product_id' => $request->product_id,
                'quantity' => $request->quantity,
                'size_id' => $sizeId,
                'size' => $request->size, // Keep for backward compatibility
                'options' => $request->options
            ]);
        }

        $cartCount = $this->getCartCount();

        return response()->json([
            'success' => true,
            'message' => 'Product added to cart successfully!',
            'cart_count' => $cartCount
        ]);
    }

    public function update(Request $request)
    {
        $request->validate([
            'item_id' => 'required|integer',
            'quantity' => 'required|integer|min:1'
        ]);

        $cartItem = $this->findCartItem($request->item_id);

        if (!$cartItem) {
            return response()->json([
                'success' => false,
                'message' => 'Cart item not found.'
            ], 404);
        }

        $product = $cartItem->product;

        // Check stock
        if ($product->manage_stock && $product->stock_quantity < $request->quantity) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient stock available.'
            ], 400);
        }

        $cartItem->update(['quantity' => $request->quantity]);

        // Recalculate totals
        $totals = $this->calculateTotals();

        return response()->json([
            'success' => true,
            'message' => 'Cart updated successfully!',
            'cart_count' => $this->getCartCount(),
            'subtotal' => $totals['subtotal'],
            'discount' => $totals['discount'],
            'tax' => $totals['tax'],
            'total' => $totals['total']
        ]);
    }

    public function remove(Request $request)
    {
        $request->validate([
            'item_id' => 'required|integer'
        ]);

        $cartItem = $this->findCartItem($request->item_id);

        if (!$cartItem) {
            return response()->json([
                'success' => false,
                'message' => 'Cart item not found.'
            ], 404);
        }

        $cartItem->delete();

        // Recalculate totals
        $totals = $this->calculateTotals();

        return response()->json([
            'success' => true,
            'message' => 'Item removed from cart!',
            'cart_count' => $this->getCartCount(),
            'subtotal' => $totals['subtotal'],
            'discount' => $totals['discount'],
            'tax' => $totals['tax'],
            'total' => $totals['total']
        ]);
    }

    public function clear()
    {
        // Always use session-based cart for guest experience
        $sessionId = Session::getId();

        CartItem::where('session_id', $sessionId)->delete();

        return response()->json([
            'success' => true,
            'message' => 'Cart cleared successfully!'
        ]);
    }

    public function count()
    {
        return response()->json([
            'count' => $this->getCartCount()
        ]);
    }

    private function getCartItems()
    {
        // Always use session-based cart for guest experience
        $sessionId = Session::getId();

        return CartItem::getCartItems(null, $sessionId);
    }

    private function getCartTotal()
    {
        // Always use session-based cart for guest experience
        $sessionId = Session::getId();

        return CartItem::getCartTotal(null, $sessionId);
    }

    private function getCartCount()
    {
        // Always use session-based cart for guest experience
        $sessionId = Session::getId();

        return CartItem::getCartCount(null, $sessionId);
    }

    private function findCartItem($id)
    {
        // Always use session-based cart for guest experience
        $sessionId = Session::getId();

        return CartItem::where('id', $id)
            ->where('session_id', $sessionId)
            ->first();
    }

    private function calculateTotals()
    {
        $cartItems = $this->getCartItems();

        $subtotal = 0;
        $discount = 0;

        foreach ($cartItems as $item) {
            // Use current price (sale price if on sale, otherwise regular price)
            $itemPrice = $item->product->isOnSale() ? $item->product->sale_price : $item->product->price;
            $itemTotal = $itemPrice * $item->quantity;
            $subtotal += $itemTotal;
        }

        // Calculate shipping using dynamic settings
        $freeShippingThreshold = \App\Models\Setting::get('free_shipping_threshold', 25000);
        $shippingCharge = \App\Models\Setting::get('shipping_charge', 500);
        $shipping = $subtotal >= $freeShippingThreshold ? 0 : $shippingCharge;

        // No tax calculation - removed GST
        $tax = 0;

        // Calculate total (no tax)
        $total = $subtotal + $shipping;

        // Set discount to 0 since we removed the discount feature
        $discount = 0;

        return [
            'subtotal' => $subtotal,
            'discount' => $discount,
            'shipping' => $shipping,
            'tax' => $tax,
            'total' => $total
        ];
    }

    private function calculateTotalsWithPromo()
    {
        $totals = $this->calculateTotals();
        $promoDiscount = 0;

        // Check if there's an applied promocode
        $appliedPromo = session('applied_promocode');
        if ($appliedPromo) {
            $promoDiscount = $appliedPromo['discount'];
        }

        // Recalculate total with promo discount (no tax)
        $tax = 0;
        $total = max(0, $totals['subtotal'] - $totals['discount'] - $promoDiscount + $totals['shipping']);

        return [
            'subtotal' => $totals['subtotal'],
            'discount' => $totals['discount'],
            'promo_discount' => $promoDiscount,
            'shipping' => $totals['shipping'],
            'tax' => $tax,
            'total' => $total
        ];
    }

    public function moveToWishlist(Request $request)
    {
        $request->validate([
            'item_id' => 'required|integer'
        ]);

        $cartItem = $this->findCartItem($request->item_id);

        if (!$cartItem) {
            return response()->json([
                'success' => false,
                'message' => 'Cart item not found.'
            ], 404);
        }

        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please login to use wishlist.'
            ], 401);
        }

        // Check if item already in wishlist
        $existingWishlist = \App\Models\Wishlist::where('user_id', Auth::id())
            ->where('product_id', $cartItem->product_id)
            ->first();

        if (!$existingWishlist) {
            \App\Models\Wishlist::create([
                'user_id' => Auth::id(),
                'product_id' => $cartItem->product_id
            ]);
        }

        $cartItem->delete();

        // Recalculate totals
        $totals = $this->calculateTotals();

        return response()->json([
            'success' => true,
            'message' => 'Item moved to wishlist!',
            'cart_count' => $this->getCartCount(),
            'subtotal' => $totals['subtotal'],
            'discount' => $totals['discount'],
            'tax' => $totals['tax'],
            'total' => $totals['total']
        ]);
    }

    public function applyPromo(Request $request)
    {
        $request->validate([
            'promo_code' => 'required|string'
        ]);

        $promoCode = strtoupper(trim($request->promo_code));

        // Find the promocode
        $promocode = Promocode::where('code', $promoCode)->first();

        if (!$promocode) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid promo code.'
            ], 400);
        }

        // Check if promocode is valid
        if (!$promocode->isValid()) {
            return response()->json([
                'success' => false,
                'message' => 'This promo code has expired or is no longer valid.'
            ], 400);
        }

        $userId = auth()->id();
        $sessionId = session()->getId();

        // Check if user can use this promocode
        if (!$promocode->canBeUsedBy($userId, $sessionId)) {
            return response()->json([
                'success' => false,
                'message' => 'You have already used this promo code the maximum number of times.'
            ], 400);
        }

        // Get cart items and calculate totals
        $cartItems = $this->getCartItems();

        if ($cartItems->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Your cart is empty.'
            ], 400);
        }

        // Calculate current totals
        $totals = $this->calculateTotals();

        // Calculate promocode discount
        $promoDiscount = $promocode->calculateDiscount($totals['subtotal'], $cartItems);

        if ($promoDiscount <= 0) {
            $minAmount = $promocode->minimum_amount;
            return response()->json([
                'success' => false,
                'message' => $minAmount > 0
                    ? "Minimum order amount of ₹" . number_format($minAmount) . " required for this promo code."
                    : 'This promo code is not applicable to your current cart.'
            ], 400);
        }

        // Store promocode in session
        session(['applied_promocode' => [
            'id' => $promocode->id,
            'code' => $promocode->code,
            'discount' => $promoDiscount,
            'type' => $promocode->type,
            'value' => $promocode->value
        ]]);

        // Recalculate totals with promocode discount
        $newTotals = $this->calculateTotalsWithPromo();

        return response()->json([
            'success' => true,
            'message' => 'Promo code applied successfully!',
            'promocode' => [
                'code' => $promocode->code,
                'discount' => $promoDiscount,
                'formatted_discount' => '₹' . number_format($promoDiscount)
            ],
            'subtotal' => $newTotals['subtotal'],
            'discount' => $newTotals['discount'],
            'promo_discount' => $newTotals['promo_discount'],
            'tax' => $newTotals['tax'],
            'shipping' => $newTotals['shipping'],
            'total' => $newTotals['total']
        ]);
    }

    public function removePromo()
    {
        session()->forget('applied_promocode');

        $totals = $this->calculateTotals();

        return response()->json([
            'success' => true,
            'message' => 'Promo code removed.',
            'subtotal' => $totals['subtotal'],
            'discount' => $totals['discount'],
            'promo_discount' => 0,
            'tax' => $totals['tax'],
            'shipping' => $totals['shipping'],
            'total' => $totals['total']
        ]);
    }
}
