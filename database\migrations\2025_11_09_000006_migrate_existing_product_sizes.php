<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\Product;
use App\Models\Size;
use App\Models\ProductSize;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, run the size seeder to ensure sizes exist
        $seeder = new \Database\Seeders\SizeSeeder();
        $seeder->run();

        // Migrate existing product sizes
        $products = Product::whereNotNull('sizes')->get();
        
        foreach ($products as $product) {
            if (is_array($product->sizes) && !empty($product->sizes)) {
                foreach ($product->sizes as $sizeName) {
                    // Find or create the size
                    $size = Size::where('name', $sizeName)->first();
                    
                    if (!$size) {
                        // Create a general size if not found
                        $size = Size::create([
                            'name' => $sizeName,
                            'display_name' => $sizeName,
                            'category_type' => 'general',
                            'sort_order' => 999,
                        ]);
                    }
                    
                    // Create ProductSize record
                    ProductSize::firstOrCreate([
                        'product_id' => $product->id,
                        'size_id' => $size->id,
                    ], [
                        'stock_quantity' => $product->stock_quantity ?? 0,
                        'is_available' => true,
                    ]);
                }
            } else {
                // Product has no sizes, create a "One Size" entry
                $oneSize = Size::where('name', 'OS')->where('category_type', 'general')->first();
                
                if ($oneSize) {
                    ProductSize::firstOrCreate([
                        'product_id' => $product->id,
                        'size_id' => $oneSize->id,
                    ], [
                        'stock_quantity' => $product->stock_quantity ?? 0,
                        'is_available' => true,
                    ]);
                }
            }
        }

        // Update products that don't have sizes at all
        $productsWithoutSizes = Product::whereNull('sizes')->orWhere('sizes', '[]')->get();
        
        foreach ($productsWithoutSizes as $product) {
            $oneSize = Size::where('name', 'OS')->where('category_type', 'general')->first();
            
            if ($oneSize) {
                ProductSize::firstOrCreate([
                    'product_id' => $product->id,
                    'size_id' => $oneSize->id,
                ], [
                    'stock_quantity' => $product->stock_quantity ?? 0,
                    'is_available' => true,
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove all ProductSize records
        ProductSize::truncate();
    }
};
