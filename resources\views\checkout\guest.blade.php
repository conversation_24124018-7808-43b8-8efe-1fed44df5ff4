@extends('layouts.app')

@section('title', 'Checkout - Kanha Fashion Hub')
@section('description', 'Complete your jewelry purchase securely with Kanha Fashion Hub.')

@section('content')
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Mobile Authentication Section -->
            <div class="col-lg-7 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h4 class="font-playfair mb-4">
                            <i class="fas fa-mobile-alt text-primary-pink me-2"></i>
                            Verify Your Mobile Number
                        </h4>
                        
                        <p class="text-muted mb-4">
                            To complete your order, please verify your mobile number. We'll send you order updates via SMS.
                        </p>

                        <!-- Mobile Number Form -->
                        <form id="checkoutMobileForm">
                            @csrf
                            <input type="hidden" name="purpose" value="checkout">

                            <div class="mb-3">
                                <label for="checkout_name" class="form-label">Your Name</label>
                                <input type="text" class="form-control form-control-lg"
                                       id="checkout_name" name="name"
                                       placeholder="Enter your full name" required>
                                <div class="invalid-feedback" id="checkoutNameError"></div>
                            </div>

                            <div class="mb-4">
                                <label for="checkout_phone" class="form-label">Mobile Number</label>
                                <div class="input-group">
                                    <span class="input-group-text">+91</span>
                                    <input type="tel" class="form-control form-control-lg"
                                           id="checkout_phone" name="phone"
                                           placeholder="Enter 10-digit mobile number"
                                           maxlength="10" required>
                                </div>
                                <div class="invalid-feedback" id="checkoutPhoneError"></div>
                            </div>

                            <button type="submit" class="btn btn-primary-pink btn-lg w-100" id="sendCheckoutOtpBtn">
                                <i class="fas fa-paper-plane me-2"></i>Send Verification Code
                            </button>
                        </form>

                        <!-- OTP Verification (Hidden initially) -->
                        <div id="otpSection" class="d-none mt-4">
                            <hr>
                            <h5 class="mb-3">Enter Verification Code</h5>
                            <p class="text-muted mb-3">
                                We've sent a 6-digit code to <strong id="displayPhone"></strong>
                            </p>

                            <form id="checkoutOtpForm">
                                @csrf
                                <input type="hidden" name="phone" id="hidden_phone">
                                <input type="hidden" name="purpose" value="checkout">
                                
                                <div class="mb-3">
                                    <input type="text" class="form-control form-control-lg text-center" 
                                           id="checkout_otp" name="otp_code" placeholder="000000" 
                                           maxlength="6" required>
                                    <div class="invalid-feedback" id="checkoutOtpError"></div>
                                </div>

                                <!-- Name field for new users -->
                                <div class="mb-3 d-none" id="checkoutNameField">
                                    <label for="checkout_name" class="form-label">Your Name</label>
                                    <input type="text" class="form-control" 
                                           id="checkout_name" name="name" placeholder="Enter your full name">
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary-pink flex-fill" id="verifyCheckoutBtn">
                                        <i class="fas fa-check me-2"></i>Verify & Continue
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="resendCheckoutOtp()">
                                        <i class="fas fa-redo me-2"></i>Resend
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Alternative Options -->
                        <div class="mt-4 pt-4 border-top">
                            <p class="text-muted mb-3">Already have an account?</p>
                            <a href="{{ route('login') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-mobile-alt me-2"></i>Login with Existing Account
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Summary Section -->
            <div class="col-lg-5">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h4 class="font-playfair mb-4">
                            <i class="fas fa-shopping-bag text-primary-pink me-2"></i>
                            Order Summary
                        </h4>

                        <!-- Cart Items -->
                        <div class="mb-4">
                            @foreach($cartSummary['items'] as $item)
                            <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                                <img src="{{ $item->product->main_image_url }}" 
                                     alt="{{ $item->product->name }}" 
                                     class="rounded" style="width: 60px; height: 60px; object-fit: cover;">
                                <div class="ms-3 flex-grow-1">
                                    <h6 class="mb-1">{{ $item->product->name }}</h6>
                                    <small class="text-muted">
                                        Qty: {{ $item->quantity }}
                                        @if($item->size_id && $item->size)
                                            | Size: {{ $item->size->full_display_name }}
                                        @endif
                                    </small>
                                    <div class="fw-semibold">₹{{ number_format($item->total_price, 2) }}</div>
                                </div>
                            </div>
                            @endforeach
                        </div>

                        <!-- Order Totals -->
                        <div class="border-top pt-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal ({{ $cartSummary['item_count'] }} items)</span>
                                <span>₹{{ number_format($cartSummary['subtotal'], 2) }}</span>
                            </div>
                            
                            @if($cartSummary['discount'] > 0)
                            <div class="d-flex justify-content-between mb-2 text-success">
                                <span>Discount</span>
                                <span>-₹{{ number_format($cartSummary['discount'], 2) }}</span>
                            </div>
                            @endif
                            
                            <div class="d-flex justify-content-between mb-2">
                                <span>Shipping</span>
                                <span>
                                    @if($cartSummary['shipping'] == 0)
                                        <span class="text-success">FREE</span>
                                    @else
                                        ₹{{ number_format($cartSummary['shipping'], 2) }}
                                    @endif
                                </span>
                            </div>
                            

                            
                            <hr>
                            
                            <div class="d-flex justify-content-between fw-bold fs-5">
                                <span>Total</span>
                                <span class="text-primary-pink">₹{{ number_format($cartSummary['total'], 2) }}</span>
                            </div>
                        </div>

                        <!-- Free Shipping Info -->
                        @if(!$cartSummary['free_shipping_eligible'] && $cartSummary['free_shipping_remaining'] > 0)
                        <div class="alert alert-info mt-3">
                            <small>
                                <i class="fas fa-truck me-2"></i>
                                Add ₹{{ number_format($cartSummary['free_shipping_remaining'], 2) }} more for FREE shipping!
                            </small>
                        </div>
                        @endif

                        <!-- Security Info -->
                        <div class="mt-4 pt-3 border-top">
                            <div class="d-flex align-items-center text-muted">
                                <i class="fas fa-shield-alt text-success me-2"></i>
                                <small>Secure checkout with 256-bit SSL encryption</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
    // Format phone number input
    document.getElementById('checkout_phone').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 10) {
            value = value.slice(0, 10);
        }
        e.target.value = value;
        clearErrors();
    });

    // Handle mobile form submission
    document.getElementById('checkoutMobileForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const phone = document.getElementById('checkout_phone').value;
        if (!/^[0-9]{10}$/.test(phone)) {
            showError('checkout_phone', 'Please enter a valid 10-digit mobile number');
            return;
        }

        sendOtp(phone);
    });

    // Handle OTP form submission
    document.getElementById('checkoutOtpForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const otpCode = document.getElementById('checkout_otp').value;
        if (!/^[0-9]{6}$/.test(otpCode)) {
            showError('checkout_otp', 'Please enter a valid 6-digit OTP');
            return;
        }

        verifyOtp();
    });

    function sendOtp(phone) {
        const name = document.getElementById('checkout_name').value.trim();

        // Validate name
        if (!name || name.length < 2) {
            showError('checkout_name', 'Please enter your full name');
            return;
        }

        const btn = document.getElementById('sendCheckoutOtpBtn');
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';

        const formData = new FormData(document.getElementById('checkoutMobileForm'));

        fetch('{{ route("mobile.send-otp") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show OTP section
                document.getElementById('otpSection').classList.remove('d-none');
                document.getElementById('displayPhone').textContent = '+91-' + phone.substr(0, 5) + '-' + phone.substr(5);
                document.getElementById('hidden_phone').value = phone;
                document.getElementById('checkout_otp').focus();
            } else {
                showError('checkout_phone', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('checkout_phone', 'Failed to send OTP. Please try again.');
        })
        .finally(() => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Send Verification Code';
        });
    }

    function verifyOtp() {
        const btn = document.getElementById('verifyCheckoutBtn');
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Verifying...';

        const formData = new FormData(document.getElementById('checkoutOtpForm'));

        fetch('{{ route("mobile.verify-otp") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Redirect to authenticated checkout
                window.location.href = data.redirect;
            } else {
                showError('checkout_otp', data.message);
                
                // Show name field for new users if needed
                if (data.message.includes('name')) {
                    document.getElementById('checkoutNameField').classList.remove('d-none');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('checkout_otp', 'Verification failed. Please try again.');
        })
        .finally(() => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-check me-2"></i>Verify & Continue';
        });
    }

    function resendCheckoutOtp() {
        fetch('{{ route("mobile.resend-otp") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess('OTP sent successfully!');
            } else {
                showError('checkout_otp', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('checkout_otp', 'Failed to resend OTP.');
        });
    }

    function showError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const errorDiv = document.getElementById(fieldId.replace('_', '') + 'Error');
        
        field.classList.add('is-invalid');
        if (errorDiv) {
            errorDiv.textContent = message;
        }
    }

    function showSuccess(message) {
        // Show success toast or alert
        alert(message);
    }

    function clearErrors() {
        const fields = ['checkout_name', 'checkout_phone', 'checkout_otp'];
        fields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            const errorDiv = document.getElementById(fieldId.replace('_', '') + 'Error');

            if (field) field.classList.remove('is-invalid');
            if (errorDiv) errorDiv.textContent = '';
        });
    }
</script>
@endpush
