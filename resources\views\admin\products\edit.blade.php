@extends('layouts.admin')

@section('title', 'Edit Product - Admin - Kanha Fashion Hub')

@section('content')
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800 font-playfair">Edit Product</h1>
        <p class="mb-0 text-muted">Update product information for: {{ $product->name }}</p>
    </div>
    <div>
        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Products
        </a>
    </div>
</div>

<!-- Product Form -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="mb-0">Product Information</h5>
            </div>
            <div class="card-body">
                <form id="productForm" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Product Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ $product->name }}" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="sku" class="form-label">SKU <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="sku" name="sku" value="{{ $product->sku }}" required>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ $product->category_id == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="stock_quantity" class="form-label">Stock Quantity <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" value="{{ $product->stock_quantity }}" min="0" required>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Products with 0 stock will be marked as "Out of Stock" and won't show "Add to Cart" button
                                @if($product->stock_quantity == 0)
                                    <br><span class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>This product is currently out of stock</span>
                                @endif
                            </small>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>

                    <!-- Pricing -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="price" class="form-label">Regular Price (₹) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="price" name="price" value="{{ $product->price }}" step="0.01" min="0" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="sale_price" class="form-label">Sale Price (₹)</label>
                            <input type="number" class="form-control" id="sale_price" name="sale_price" value="{{ $product->sale_price }}" step="0.01" min="0">
                            <small class="form-text text-muted">Leave empty if not on sale</small>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>

                    <!-- Descriptions -->
                    <div class="mb-3">
                        <label for="short_description" class="form-label">Short Description</label>
                        <textarea class="form-control" id="short_description" name="short_description" rows="2" placeholder="Brief product description for listings">{{ $product->short_description }}</textarea>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Full Description <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="description" name="description" rows="4" placeholder="Detailed product description">{{ $product->description }}</textarea>
                        <div class="invalid-feedback"></div>
                    </div>

                    <!-- Jewelry Specifications -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">Jewelry Specifications</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="weight" class="form-label">Weight (grams)</label>
                                    <input type="number" class="form-control" id="weight" name="weight" value="{{ $product->weight }}" step="0.01" min="0">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="metal_type" class="form-label">Metal Type</label>
                                    <select class="form-select" id="metal_type" name="metal_type">
                                        <option value="">Select Metal Type</option>
                                        <option value="gold" {{ $product->metal_type == 'gold' ? 'selected' : '' }}>Gold</option>
                                        <option value="silver" {{ $product->metal_type == 'silver' ? 'selected' : '' }}>Silver</option>
                                        <option value="platinum" {{ $product->metal_type == 'platinum' ? 'selected' : '' }}>Platinum</option>
                                        <option value="rose_gold" {{ $product->metal_type == 'rose_gold' ? 'selected' : '' }}>Rose Gold</option>
                                        <option value="white_gold" {{ $product->metal_type == 'white_gold' ? 'selected' : '' }}>White Gold</option>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="metal_purity" class="form-label">Metal Purity</label>
                                    <select class="form-select" id="metal_purity" name="metal_purity">
                                        <option value="">Select Purity</option>
                                        <option value="14k" {{ $product->metal_purity == '14k' ? 'selected' : '' }}>14K</option>
                                        <option value="18k" {{ $product->metal_purity == '18k' ? 'selected' : '' }}>18K</option>
                                        <option value="22k" {{ $product->metal_purity == '22k' ? 'selected' : '' }}>22K</option>
                                        <option value="24k" {{ $product->metal_purity == '24k' ? 'selected' : '' }}>24K</option>
                                        <option value="925" {{ $product->metal_purity == '925' ? 'selected' : '' }}>925 Sterling Silver</option>
                                        <option value="950" {{ $product->metal_purity == '950' ? 'selected' : '' }}>950 Platinum</option>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="stone_type" class="form-label">Stone Type</label>
                                    <input type="text" class="form-control" id="stone_type" name="stone_type" value="{{ $product->stone_type }}" placeholder="e.g., Diamond, Ruby, Emerald">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="stone_weight" class="form-label">Stone Weight (carats)</label>
                                    <input type="number" class="form-control" id="stone_weight" name="stone_weight" value="{{ $product->stone_weight }}" step="0.01" min="0">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="certification" class="form-label">Certification</label>
                                    <input type="text" class="form-control" id="certification" name="certification" value="{{ $product->certification }}" placeholder="e.g., GIA, IGI">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Images -->
                    @if($product->images && count($product->images) > 0)
                    <div class="mb-3">
                        <label class="form-label">Current Images</label>
                        <div class="row" id="currentImages">
                            @foreach($product->images as $index => $image)
                            <div class="col-auto mb-2">
                                <div class="image-preview">
                                    <img src="{{ asset('storage/' . $image) }}" alt="Product Image" style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px;">
                                    <button type="button" class="remove-current-image btn btn-sm btn-danger" data-image="{{ $image }}" style="position: absolute; top: -8px; right: -8px; border-radius: 50%; width: 24px; height: 24px; font-size: 12px;">×</button>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <!-- Product Images -->
                    <div class="mb-3">
                        <label for="images" class="form-label">Add New Images</label>
                        <input type="file" class="form-control" id="images" name="images[]" multiple accept="image/*">
                        <small class="form-text text-muted">Select new images to add. Existing images will be replaced if new ones are uploaded.</small>
                        <div class="invalid-feedback"></div>
                        <div id="imagePreview" class="mt-3 row"></div>
                    </div>

                    <!-- Size and Stock Management -->
                    <div class="mb-4">
                        <h6 class="mb-3">Size and Stock Management</h6>
                        <div id="size-stock-container">
                            @if($availableSizes && count($availableSizes) > 0)
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Size</th>
                                                <th>Stock Quantity</th>
                                                <th>Price Adjustment</th>
                                                <th>SKU Suffix</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($availableSizes as $index => $sizeInfo)
                                                <tr>
                                                    <td>
                                                        <strong>{{ $sizeInfo['display_name'] }}</strong>
                                                        <input type="hidden" name="product_sizes[{{ $index }}][size_id]" value="{{ $sizeInfo['size_id'] }}">
                                                    </td>
                                                    <td>
                                                        <input type="number" class="form-control" name="product_sizes[{{ $index }}][stock_quantity]"
                                                               value="{{ $sizeInfo['stock_quantity'] ?? 0 }}" min="0" placeholder="0">
                                                    </td>
                                                    <td>
                                                        <div class="input-group">
                                                            <span class="input-group-text">₹</span>
                                                            <input type="number" class="form-control" name="product_sizes[{{ $index }}][price_adjustment]"
                                                                   value="{{ $sizeInfo['price_adjustment'] ?? 0 }}" step="0.01" placeholder="0.00">
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" name="product_sizes[{{ $index }}][sku_suffix]"
                                                               value="{{ $sizeInfo['sku_suffix'] ?? '' }}" placeholder="e.g., -{{ $sizeInfo['name'] }}" maxlength="10">
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Set stock quantity to 0 to mark a size as out of stock. Price adjustment will be added to the base product price.
                                </small>
                            @else
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    No sizes are configured for this category. The product will use a default "One Size" option.
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Additional Options -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="specifications" class="form-label">Additional Specifications</label>
                            <textarea class="form-control" id="specifications" name="specifications" rows="2" placeholder="Any additional specifications (JSON format)">{{ is_array($product->specifications) ? json_encode($product->specifications) : $product->specifications }}</textarea>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <!-- Placeholder for future additional options -->
                        </div>
                    </div>

                    <!-- Product Settings -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">Product Settings</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1" {{ $product->is_featured ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_featured">
                                    <strong>Featured Product</strong>
                                    <small class="d-block text-muted">Mark this product as featured to display it prominently on the homepage</small>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary-pink" id="submitBtn">
                            <i class="fas fa-save me-2"></i>Update Product
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .image-preview {
        position: relative;
        display: inline-block;
        margin: 5px;
    }
    
    .image-preview img {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border-radius: 8px;
        border: 2px solid #dee2e6;
    }
    
    .image-preview .remove-image,
    .image-preview .remove-current-image {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        font-size: 12px;
        cursor: pointer;
    }
</style>
@endpush

@push('scripts')
<!-- Include CKEditor 5 Classic -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize CKEditor for product description
    let descriptionEditor;
    ClassicEditor.create(document.querySelector('#description'), {
        toolbar: {
            items: [
                'heading', '|',
                'bold', 'italic', '|',
                'bulletedList', 'numberedList', '|',
                'outdent', 'indent', '|',
                'blockQuote', '|',
                'undo', 'redo'
            ]
        },
        heading: {
            options: [
                { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                { model: 'heading2', view: 'h2', title: 'Heading', class: 'ck-heading_heading2' }
            ]
        }
    })
    .then(editor => {
        descriptionEditor = editor;
    })
    .catch(error => {
        console.error('CKEditor initialization error:', error);
    });
    const form = document.getElementById('productForm');
    const submitBtn = document.getElementById('submitBtn');
    const imageInput = document.getElementById('images');
    const imagePreview = document.getElementById('imagePreview');
    
    // Image preview functionality
    imageInput.addEventListener('change', function(e) {
        imagePreview.innerHTML = '';
        const files = Array.from(e.target.files);
        
        files.forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const col = document.createElement('div');
                    col.className = 'col-auto';
                    col.innerHTML = `
                        <div class="image-preview">
                            <img src="${e.target.result}" alt="Preview">
                            <button type="button" class="remove-image" data-index="${index}">×</button>
                        </div>
                    `;
                    imagePreview.appendChild(col);
                };
                reader.readAsDataURL(file);
            }
        });
    });
    
    // Remove new image functionality
    imagePreview.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-image')) {
            const index = parseInt(e.target.dataset.index);
            const dt = new DataTransfer();
            const files = Array.from(imageInput.files);
            
            files.forEach((file, i) => {
                if (i !== index) {
                    dt.items.add(file);
                }
            });
            
            imageInput.files = dt.files;
            imageInput.dispatchEvent(new Event('change'));
        }
    });
    
    // Remove current image functionality
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-current-image')) {
            e.target.closest('.col-auto').remove();
        }
    });
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate CKEditor content
        if (descriptionEditor) {
            const editorData = descriptionEditor.getData().trim();
            if (!editorData || editorData === '<p>&nbsp;</p>' || editorData === '<p></p>') {
                alert('Please enter a product description.');
                descriptionEditor.editing.view.focus();
                return;
            }
            // Update the hidden textarea with CKEditor content
            document.getElementById('description').value = editorData;
        }

        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';

        // Clear previous errors
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
        
        const formData = new FormData(form);
        
        // Handle product sizes data
        const sizeInputs = document.querySelectorAll('[name^="product_sizes"]');
        const productSizes = [];
        const sizeGroups = {};

        // Group size inputs by index
        sizeInputs.forEach(input => {
            const match = input.name.match(/product_sizes\[(\d+)\]\[(\w+)\]/);
            if (match) {
                const index = match[1];
                const field = match[2];
                if (!sizeGroups[index]) sizeGroups[index] = {};
                sizeGroups[index][field] = input.value;
            }
        });

        // Convert to array and add to formData
        Object.values(sizeGroups).forEach((sizeData, index) => {
            if (sizeData.size_id && sizeData.stock_quantity !== undefined) {
                formData.append(`product_sizes[${index}][size_id]`, sizeData.size_id);
                formData.append(`product_sizes[${index}][stock_quantity]`, sizeData.stock_quantity || 0);
                formData.append(`product_sizes[${index}][price_adjustment]`, sizeData.price_adjustment || 0);
                formData.append(`product_sizes[${index}][sku_suffix]`, sizeData.sku_suffix || '');
            }
        });

        // Convert specifications to array if provided
        const specsInput = document.getElementById('specifications').value;
        if (specsInput) {
            formData.delete('specifications');
            try {
                const specsObj = JSON.parse(specsInput);
                Object.keys(specsObj).forEach(key => {
                    formData.append(`specifications[${key}]`, specsObj[key]);
                });
            } catch (e) {
                // If not valid JSON, treat as plain text description
                formData.append('specifications[description]', specsInput);
            }
        }
        
        fetch('{{ route("admin.products.update", $product->id) }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(async response => {
            const data = await response.json();

            if (response.ok && data.success) {
                // Show success message
                const alert = document.createElement('div');
                alert.className = 'alert alert-success alert-dismissible fade show';
                alert.innerHTML = `
                    ${data.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                form.insertBefore(alert, form.firstChild);

                // Scroll to top to show message
                window.scrollTo(0, 0);
                return;
            }

            // Handle errors
            let errorMessage = 'An error occurred while updating the product.';
            let validationErrors = null;

            if (response.status === 422) {
                // Validation errors
                errorMessage = data.message || 'Please check the form for errors.';
                validationErrors = data.errors;
            } else if (response.status === 400) {
                // InvalidArgumentException or other client errors
                errorMessage = data.message || errorMessage;
            } else if (data.message) {
                errorMessage = data.message;
            }

            // Show error message
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show';
            alert.innerHTML = `
                <strong>Error:</strong> ${errorMessage}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            form.insertBefore(alert, form.firstChild);

            // Handle validation errors
            if (validationErrors) {
                Object.keys(validationErrors).forEach(field => {
                    const input = document.querySelector(`[name="${field}"]`);
                    if (input) {
                        input.classList.add('is-invalid');
                        const feedback = input.parentNode.querySelector('.invalid-feedback');
                        if (feedback) {
                            feedback.textContent = validationErrors[field][0];
                        }
                    }
                });

                // Show detailed validation errors in alert
                const errorList = Object.keys(validationErrors).map(field =>
                    `• ${field}: ${validationErrors[field][0]}`
                ).join('<br>');

                const detailAlert = document.createElement('div');
                detailAlert.className = 'alert alert-warning alert-dismissible fade show mt-2';
                detailAlert.innerHTML = `
                    <strong>Validation Errors:</strong><br>
                    ${errorList}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                form.insertBefore(detailAlert, form.children[1]);
            }

            // Scroll to top to show message
            window.scrollTo(0, 0);
        })
        .catch(error => {
            console.error('Network or parsing error:', error);

            // Show network error message
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show';
            alert.innerHTML = `
                <strong>Network Error:</strong> Unable to connect to server. Please check your connection and try again.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            form.insertBefore(alert, form.firstChild);

            // Scroll to top to show message
            window.scrollTo(0, 0);
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Update Product';
        });
    });

    // Handle category change to load available sizes
    const categorySelect = document.getElementById('category_id');
    const sizeStockContainer = document.getElementById('size-stock-container');

    // Function to load sizes for a category
    function loadSizesForCategory(categoryId) {
        if (categoryId) {
            // Show loading
            sizeStockContainer.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    Loading available sizes for this category...
                </div>
            `;

            // Fetch available sizes for the selected category
            fetch(`/admin/api/categories/${categoryId}/sizes`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.sizes.length > 0) {
                        // Build size-stock matrix
                        let html = `
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Size</th>
                                            <th>Stock Quantity</th>
                                            <th>Price Adjustment</th>
                                            <th>SKU Suffix</th>
                                            <th>Available</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                        `;

                        data.sizes.forEach(size => {
                            html += `
                                <tr>
                                    <td>
                                        <strong>${size.display_name}</strong>
                                        <input type="hidden" name="product_sizes[${size.id}][size_id]" value="${size.id}">
                                    </td>
                                    <td>
                                        <input type="number"
                                               class="form-control"
                                               name="product_sizes[${size.id}][stock_quantity]"
                                               value="0"
                                               min="0"
                                               placeholder="0">
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <span class="input-group-text">₹</span>
                                            <input type="number"
                                                   class="form-control"
                                                   name="product_sizes[${size.id}][price_adjustment]"
                                                   value="0.00"
                                                   step="0.01"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <input type="text"
                                               class="form-control"
                                               name="product_sizes[${size.id}][sku_suffix]"
                                               value=""
                                               placeholder="e.g., -${size.name}">
                                    </td>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input"
                                                   type="checkbox"
                                                   name="product_sizes[${size.id}][is_available]"
                                                   value="1"
                                                   checked>
                                            <label class="form-check-label">Available</label>
                                        </div>
                                    </td>
                                </tr>
                            `;
                        });

                        html += `
                                    </tbody>
                                </table>
                            </div>
                        `;

                        sizeStockContainer.innerHTML = html;
                    } else {
                        sizeStockContainer.innerHTML = `
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                No sizes available for this category. Please configure sizes for this category first.
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error loading sizes:', error);
                    sizeStockContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            Error loading sizes for this category. Please try again.
                        </div>
                    `;
                });
        } else {
            // No category selected
            sizeStockContainer.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Please select a category first to load available sizes.
                </div>
            `;
        }
    }

    // Load sizes on category change
    categorySelect.addEventListener('change', function() {
        loadSizesForCategory(this.value);
    });

    // Load sizes on page load if category is already selected
    if (categorySelect.value) {
        loadSizesForCategory(categorySelect.value);
    }
});
</script>
@endpush
