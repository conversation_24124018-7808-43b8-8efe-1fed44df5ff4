<?php

namespace Database\Seeders;

use App\Models\Page;
use Illuminate\Database\Seeder;

class PageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pages = [
            [
                'title' => 'About Us',
                'slug' => 'about',
                'content' => $this->getAboutContent(),
                'excerpt' => 'Discover the story behind Kanha Fashion Hub. Learn about our heritage, craftsmanship, and commitment to creating quality fashion accessories.',
                'meta_title' => 'About Us - Kanha Fashion Hub',
                'meta_description' => 'Discover the story behind Kanha Fashion Hub. Learn about our heritage, craftsmanship, and commitment to creating quality fashion accessories.',
                'meta_keywords' => ['about', 'fashion accessories', 'heritage', 'craftsmanship', 'kanha fashion hub'],
                'template' => 'about',
                'is_published' => true,
                'show_in_menu' => true,
                'menu_order' => 1,
                'published_at' => now(),
            ],
            [
                'title' => 'Contact Us',
                'slug' => 'contact',
                'content' => $this->getContactContent(),
                'excerpt' => 'Get in touch with Kanha Fashion Hub. Visit our store, call us, or send us a message for any inquiries about our fashion accessories.',
                'meta_title' => 'Contact Us - Kanha Fashion Hub',
                'meta_description' => 'Get in touch with Kanha Fashion Hub. Visit our store, call us, or send us a message for any inquiries about our fashion accessories.',
                'meta_keywords' => ['contact', 'fashion accessories', 'customer service', 'kanha fashion hub'],
                'template' => 'contact',
                'is_published' => true,
                'show_in_menu' => true,
                'menu_order' => 2,
                'published_at' => now(),
            ],
            [
                'title' => 'Shipping Information',
                'slug' => 'shipping',
                'content' => $this->getShippingContent(),
                'excerpt' => 'Learn about our shipping policies, delivery times, and shipping costs for fashion accessory orders.',
                'meta_title' => 'Shipping Information - Kanha Fashion Hub',
                'meta_description' => 'Learn about our shipping policies, delivery times, and shipping costs for fashion accessory orders.',
                'meta_keywords' => ['shipping', 'delivery', 'fashion accessories', 'policies'],
                'template' => 'guide',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 10,
                'published_at' => now(),
            ],
            [
                'title' => 'Returns & Exchange Policy',
                'slug' => 'returns',
                'content' => $this->getReturnsContent(),
                'excerpt' => 'Hassle-free returns and exchanges designed to support your business and ensure satisfaction.',
                'meta_title' => 'Returns & Exchange Policy - Kanha Fashion Hub',
                'meta_description' => 'Hassle-free returns and exchanges designed to support your business and ensure satisfaction.',
                'meta_keywords' => ['returns', 'exchange', 'policy', 'business support'],
                'template' => 'policy',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 11,
                'published_at' => now(),
            ],
            [
                'title' => 'Size Guide',
                'slug' => 'size-guide',
                'content' => $this->getSizeGuideContent(),
                'excerpt' => 'Find the perfect fit with our comprehensive size guide for rings, bangles, necklaces, and fashion accessories.',
                'meta_title' => 'Size Guide - Kanha Fashion Hub',
                'meta_description' => 'Find the perfect fit with our comprehensive size guide for rings, bangles, necklaces, and fashion accessories.',
                'meta_keywords' => ['size guide', 'ring size', 'accessory sizing', 'measurements'],
                'template' => 'guide',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 12,
                'published_at' => now(),
            ],
            [
                'title' => 'Accessory Care Guide',
                'slug' => 'accessory-care',
                'content' => $this->getAccessoryCareContent(),
                'excerpt' => 'Learn how to properly care for your fashion accessories with our comprehensive care guide and maintenance tips.',
                'meta_title' => 'Accessory Care Guide - Kanha Fashion Hub',
                'meta_description' => 'Learn how to properly care for your fashion accessories with our comprehensive care guide and maintenance tips.',
                'meta_keywords' => ['accessory care', 'maintenance', 'cleaning', 'storage'],
                'template' => 'guide',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 13,
                'published_at' => now(),
            ],

            [
                'title' => 'Privacy Policy',
                'slug' => 'privacy-policy',
                'content' => $this->getPrivacyPolicyContent(),
                'excerpt' => 'Read our privacy policy to understand how we collect, use, and protect your personal information at Kanha Fashion Hub.',
                'meta_title' => 'Privacy Policy - Kanha Fashion Hub',
                'meta_description' => 'Read our privacy policy to understand how we collect, use, and protect your personal information at Kanha Fashion Hub.',
                'meta_keywords' => ['privacy policy', 'data protection', 'personal information'],
                'template' => 'legal',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 20,
                'published_at' => now(),
            ],
            [
                'title' => 'Terms of Service',
                'slug' => 'terms-of-service',
                'content' => $this->getTermsOfServiceContent(),
                'excerpt' => 'Read our terms of service to understand the rules and regulations for using Kanha Fashion Hub website and services.',
                'meta_title' => 'Terms of Service - Kanha Fashion Hub',
                'meta_description' => 'Read our terms of service to understand the rules and regulations for using Kanha Fashion Hub website and services.',
                'meta_keywords' => ['terms of service', 'terms and conditions', 'legal'],
                'template' => 'legal',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 21,
                'published_at' => now(),
            ],
            [
                'title' => 'Cookie Policy',
                'slug' => 'cookie-policy',
                'content' => $this->getCookiePolicyContent(),
                'excerpt' => 'Learn about how we use cookies and similar technologies to enhance your browsing experience on Kanha Fashion Hub website.',
                'meta_title' => 'Cookie Policy - Kanha Fashion Hub',
                'meta_description' => 'Learn about how we use cookies and similar technologies to enhance your browsing experience on Kanha Fashion Hub website.',
                'meta_keywords' => ['cookie policy', 'cookies', 'tracking', 'privacy'],
                'template' => 'legal',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 22,
                'published_at' => now(),
            ],
        ];

        foreach ($pages as $pageData) {
            Page::updateOrCreate(
                ['slug' => $pageData['slug']],
                $pageData
            );
        }

        $this->command->info('Pages seeded successfully!');
    }

    private function getAboutContent(): string
    {
        return '<h2>Our Story</h2>
<p>Kanha Fashion Hub connects businesses with quality, trendy accessories—bangles, earrings, necklaces, bags, rings and much more—offering reliability, affordability, and style. We empower retailers and wholesalers with trusted partnerships and consistent fashion solutions.</p>

<h3>Our Heritage</h3>
<p>Rooted in tradition and trust, Kanha Fashion Hub preserves timeless craftsmanship while embracing modern trends, delivering accessories that celebrate culture, quality, and long-standing partnerships in the fashion business.</p>

<h3>Our Craftsmanship</h3>
<p>At Kanha Fashion Hub, craftsmanship means precision, consistency, and reliability. We design and source accessories that meet market demands, empowering retailers and wholesalers with quality products tailored for business success.</p>

<h3>Our Values</h3>
<ul>
<li><strong>Quality:</strong> We deliver durable, trend-driven accessories crafted with precision, ensuring consistent excellence for every business partner.</li>
<li><strong>Trust:</strong> We build long-term, transparent partnerships based on honesty, reliability, and mutual growth.</li>
<li><strong>Innovation:</strong> We adapt designs to evolving market trends while honoring cultural heritage, helping businesses stay competitive.</li>
<li><strong>Service:</strong> We ensure seamless support, timely deliveries, and dedicated assistance to strengthen every client\'s success journey.</li>
</ul>

<h3>Our Promise</h3>
<p>At Kanha Fashion Hub, we promise businesses reliable supply, consistent quality, and trendy accessories—crafted with care and precision to strengthen partnerships, delight customers, and drive sustainable growth.</p>

<h3>Owner</h3>
<p>With a passion for fashion and a vision to empower businesses, <strong>Yashoda Rani Jaiswal</strong> leads Kanha Fashion Hub with creativity, integrity, and a commitment to excellence.</p>

<h3>KANHA FASHION HUB</h3>
<p>Kanha Fashion Hub supplies retailers and wholesalers with stylish, high-quality accessories that blend timeless elegance and modern trends. Each piece is crafted or sourced with care, ensuring reliability, quality, and market appeal. Together, we bring your customers the true spirit of style—our promise: <strong>Eternal Fashion Essence</strong>.</p>';
    }

    private function getContactContent(): string
    {
        $phone = config('contact.phone');
        $email = config('contact.email');
        $address = config('contact.address.full');
        $businessHours = config('contact.business_hours.display');
        $phoneFormatted = '+91-' . substr($phone, 0, 5) . '-' . substr($phone, 5);

        return '<h2>Get in Touch</h2>
<p>We\'d love to hear from you. Get in touch with our team for any questions about our fashion accessories, bulk orders, or business partnerships.</p>

<h3>Visit Our Store</h3>
<p>Experience our fashion accessory collection in person at our showroom located in Waidhan, Madhya Pradesh.</p>
<p><strong>Address:</strong><br>
Kanha Fashion Hub<br>
' . $address . '</p>

<h3>Contact Information</h3>
<p><strong>Phone:</strong> <a href="tel:+91' . $phone . '">' . $phoneFormatted . '</a><br>
<strong>Email:</strong> <a href="mailto:' . $email . '">' . $email . '</a><br>
<strong>WhatsApp:</strong> <a href="https://wa.me/91' . $phone . '" target="_blank">' . $phoneFormatted . '</a><br>
<strong>Instagram:</strong> <a href="' . config('contact.social.instagram.url') . '" target="_blank">@' . config('contact.social.instagram.username') . '</a></p>

<h3>Store Hours</h3>
<p>' . $businessHours . '</p>

<h3>Customer Service</h3>
<p>Our customer service team is available to assist you with:</p>
<ul>
<li>Product inquiries and recommendations</li>
<li>Bulk order consultations and pricing</li>
<li>Order status and tracking</li>
<li>Returns and exchanges</li>
<li>Product care and maintenance</li>
<li>Business partnership opportunities</li>
</ul>

<h3>Follow Us</h3>
<p>Stay connected with us on social media for the latest updates, new arrivals, and exclusive offers:</p>
<ul>
<li><strong>Instagram:</strong> <a href="' . config('contact.social.instagram.url') . '" target="_blank">@' . config('contact.social.instagram.username') . '</a></li>
<li><strong>Facebook:</strong> <a href="' . config('contact.social.facebook.url') . '" target="_blank">@' . config('contact.social.facebook.username') . '</a></li>
<li><strong>YouTube:</strong> <a href="' . config('contact.social.youtube.url') . '" target="_blank">@' . config('contact.social.youtube.username') . '</a></li>
</ul>';
    }

    private function getShippingContent(): string
    {
        return '<h2>Shipping Information</h2>
<p>Fast, secure, and reliable delivery for your fashion accessories from Waidhan, Madhya Pradesh.</p>

<h3>Domestic Shipping (India)</h3>
<ul>
<li><strong>Free Shipping:</strong> On orders above ₹3,000</li>
<li><strong>Standard Shipping:</strong> ₹100 for orders ₹1,500 - ₹2,999</li>
<li><strong>Express Shipping:</strong> ₹200 for orders below ₹1,500</li>
<li><strong>Delivery Time:</strong> 3-6 business days for major cities, 5-8 days for remote areas</li>
</ul>

<h3>International Shipping</h3>
<p>We ship to selected countries worldwide:</p>
<ul>
<li>USA & Canada: 10-15 business days</li>
<li>UK & Europe: 7-12 business days</li>
<li>Australia: 10-15 business days</li>
<li>UAE & Middle East: 5-10 business days</li>
</ul>

<h3>Shipping Process</h3>
<ol>
<li><strong>Order Processing:</strong> 1-2 business days for verification and preparation</li>
<li><strong>Quality Check:</strong> Each accessory is carefully inspected before packaging</li>
<li><strong>Secure Packaging:</strong> Premium packaging with protective materials and branded boxes</li>
<li><strong>Dispatch & Tracking:</strong> Tracking details sent via email and SMS</li>
</ol>

<h3>Order Tracking</h3>
<p>Track your order easily through:</p>
<ul>
<li>Your account dashboard</li>
<li>Tracking link sent to your email</li>
<li>Customer service hotline: +91-97534-47832</li>
<li>WhatsApp support: +91-97534-47832</li>
</ul>';
    }

    private function getReturnsContent(): string
    {
        return '<h2>Returns & Exchange Policy</h2>
<p>Hassle-free returns and exchanges designed to support your business and ensure satisfaction.</p>

<h3>Return Eligibility</h3>
<h4>✓ Eligible for Return:</h4>
<ul>
<li>Items in original condition with tags</li>
<li>Unworn accessories with original packaging</li>
<li>Items returned within 10 days of delivery</li>
<li>Items accompanied by original certificates or invoices</li>
</ul>

<h4>✗ Not Eligible for Return:</h4>
<ul>
<li>Customized or personalized products</li>
<li>Items damaged due to misuse or improper handling</li>
<li>Items without original packaging</li>
<li>Sale or clearance items (unless defective)</li>
</ul>

<h3>Return Process</h3>
<ol>
<li><strong>Initiate Return:</strong> Contact your account manager or log in to request a return via "My Orders."</li>
<li><strong>Pack Securely:</strong> Ensure items are in original packaging with all accessories and documentation.</li>
<li><strong>Schedule Pickup:</strong> Free pickup arranged from your business address.</li>
<li><strong>Get Refund:</strong> Refund processed within 5–7 business days after receiving the item.</li>
</ol>

<h3>Exchange Policy</h3>
<p><strong>Size Exchange:</strong> Free exchange for rings, bangles, and adjustable accessories within 30 days.</p>
<p><strong>Design Exchange:</strong> Exchange for same or higher value items with payment of the price difference, if applicable.</p>

<h3>Refund Timeline</h3>
<ul>
<li>Credit/Debit Card: 5–7 business days</li>
<li>Net Banking: 5–7 business days</li>
<li>UPI/Wallet: 3–5 business days</li>
<li>Cash on Delivery: 7–10 business days (₹50 processing fee)</li>
</ul>';
    }

    private function getSizeGuideContent(): string
    {
        return '<h2>Size Guide</h2>
<p>Find your perfect fit with our comprehensive sizing guide for fashion accessories.</p>

<h3>Ring Size Guide</h3>
<h4>How to Measure:</h4>
<ol>
<li>Wrap a string around your finger where you\'d wear the ring</li>
<li>Mark where the string overlaps</li>
<li>Measure the string length with a ruler</li>
<li>Use the chart below to find your ring size</li>
</ol>

<h4>Ring Size Chart</h4>
<table>
<tr><th>Indian Size</th><th>US Size</th><th>UK Size</th><th>Circumference (mm)</th></tr>
<tr><td>8</td><td>4</td><td>H</td><td>46.8</td></tr>
<tr><td>10</td><td>5</td><td>J</td><td>49.3</td></tr>
<tr><td>12</td><td>6</td><td>L</td><td>51.8</td></tr>
<tr><td>14</td><td>7</td><td>N</td><td>54.4</td></tr>
<tr><td>16</td><td>8</td><td>P</td><td>56.9</td></tr>
<tr><td>18</td><td>9</td><td>R</td><td>59.5</td></tr>
<tr><td>20</td><td>10</td><td>T</td><td>62.1</td></tr>
</table>

<h3>Bangle Size Guide</h3>
<p>Measure the widest part of your hand when thumb and little finger are brought together. Most bangles are available in standard sizes: Small (2.4"), Medium (2.6"), and Large (2.8").</p>

<h3>Necklace Length Guide</h3>
<ul>
<li><strong>14-16" (Choker):</strong> Sits at the base of neck - perfect for layering</li>
<li><strong>16-18" (Princess):</strong> Sits at collarbone - most versatile length</li>
<li><strong>20-24" (Matinee):</strong> Falls below collarbone - ideal for business wear</li>
<li><strong>28-36" (Opera):</strong> Falls at or below bust line - great for evening wear</li>
</ul>

<h3>Earring Guide</h3>
<ul>
<li><strong>Stud Earrings:</strong> Available in various sizes from 4mm to 12mm</li>
<li><strong>Hoop Earrings:</strong> Small (15-20mm), Medium (25-35mm), Large (40mm+)</li>
<li><strong>Drop Earrings:</strong> Short (1-2"), Medium (2-3"), Long (3"+)</li>
</ul>';
    }

    private function getAccessoryCareContent(): string
    {
        return '<h2>Accessory Care Guide</h2>
<p>Keep your fashion accessories looking beautiful and lasting longer with proper care.</p>

<h3>Metal Accessories Care</h3>
<h4>Do\'s:</h4>
<ul>
<li>Clean with a soft, dry cloth after each use</li>
<li>Use mild soap and water for deeper cleaning</li>
<li>Dry thoroughly before storing</li>
<li>Store in individual pouches or compartments</li>
<li>Remove before swimming, showering, or exercising</li>
</ul>

<h4>Don\'ts:</h4>
<ul>
<li>Use harsh chemicals, bleach, or abrasive cleaners</li>
<li>Expose to excessive moisture or humidity</li>
<li>Store different metals together (can cause tarnishing)</li>
<li>Apply perfumes, lotions, or cosmetics directly on accessories</li>
<li>Leave in direct sunlight for extended periods</li>
</ul>

<h3>Fabric & Leather Accessories</h3>
<h4>Do\'s:</h4>
<ul>
<li>Spot clean with appropriate cleaners</li>
<li>Allow to air dry completely</li>
<li>Store in dust bags or breathable containers</li>
<li>Use leather conditioner for leather items</li>
<li>Keep away from heat sources</li>
</ul>

<h3>Beaded & Stone Accessories</h3>
<ul>
<li><strong>Natural Stones:</strong> Gentle cleaning with soft cloth, avoid water</li>
<li><strong>Glass Beads:</strong> Clean with mild soap and water, dry thoroughly</li>
<li><strong>Pearls:</strong> Wipe with soft cloth only, avoid chemicals</li>
<li><strong>Crystal:</strong> Use specialized jewelry cleaner or mild soap solution</li>
</ul>

<h3>Storage Tips</h3>
<ul>
<li>Store each piece separately to prevent scratching</li>
<li>Keep in a cool, dry place away from direct sunlight</li>
<li>Use jewelry boxes with individual compartments or soft pouches</li>
<li>Regularly inspect for loose components or wear</li>
<li>Rotate accessories to prevent overuse of favorite pieces</li>
</ul>';
    }



    private function getPrivacyPolicyContent(): string
    {
        return '<h2>Privacy Policy</h2>
<p>Your privacy and data security are our top priorities at Kanha Fashion Hub.</p>

<h3>Information We Collect</h3>
<h4>Personal Information:</h4>
<ul>
<li>Name, email address, and phone number</li>
<li>Billing and shipping addresses</li>
<li>Payment information (processed securely)</li>
<li>Account credentials and preferences</li>
<li>Purchase history and order details</li>
</ul>

<h4>Automatically Collected Information:</h4>
<ul>
<li>IP address and browser details</li>
<li>Device type and operating system</li>
<li>Pages visited and time spent on our site</li>
<li>Cookies and tracking technologies</li>
</ul>

<h3>How We Use Your Information</h3>
<ul>
<li>To process and fulfill orders</li>
<li>To provide customer support</li>
<li>To send marketing communications (with your consent)</li>
<li>To improve our services and website experience</li>
<li>To prevent fraud and ensure secure transactions</li>
</ul>

<h3>Information Sharing</h3>
<p>We do not sell, trade, or rent your personal information. We may share data with:</p>
<ul>
<li>Service providers (payment processors, logistics/shipping partners)</li>
<li>Legal authorities when required by law</li>
<li>Business partners for legitimate business purposes</li>
</ul>

<h3>Data Security</h3>
<ul>
<li>SSL/TLS encryption for safe data transmission</li>
<li>Secure servers with restricted access controls</li>
<li>Regular security audits and updates</li>
<li>Trained staff for data protection practices</li>
</ul>

<h3>Your Rights</h3>
<ul>
<li>Access your personal information</li>
<li>Correct inaccurate information</li>
<li>Request deletion of your data</li>
<li>Opt-out of marketing communications anytime</li>
</ul>

<h3>Contact Us About Privacy</h3>
<p>If you have questions about this Privacy Policy or how we handle your data, please contact us:</p>
<p><strong>Phone:</strong> <a href="tel:+91' . config('contact.phone') . '">+91-' . substr(config('contact.phone'), 0, 5) . '-' . substr(config('contact.phone'), 5) . '</a><br>
<strong>Address:</strong> ' . config('contact.address.full') . '</p>';
    }

    private function getTermsOfServiceContent(): string
    {
        return '<h2>Terms of Service</h2>
<p>Please read these terms carefully before using our services.</p>

<h3>Agreement to Terms</h3>
<p>By accessing or using our website, you agree to be bound by these Terms of Service. If you do not agree with any part, you may not access our website or use our services.</p>

<h3>Use of Our Service</h3>
<h4>Permitted Uses:</h4>
<ul>
<li>Browse and purchase fashion accessories (bangles, earrings, necklaces, bags)</li>
<li>Create and manage your account</li>
<li>Contact customer service for support</li>
<li>Leave reviews and feedback</li>
</ul>

<h4>Prohibited Uses:</h4>
<ul>
<li>Violate any applicable laws or regulations</li>
<li>Transmit harmful or malicious code</li>
<li>Attempt unauthorized access to our systems</li>
<li>Interfere with or disrupt our service</li>
<li>Engage in fraudulent or misleading activities</li>
</ul>

<h3>Account Terms</h3>
<ul>
<li>You must provide accurate and complete information</li>
<li>You must be at least 18 years old to create an account</li>
<li>You are responsible for maintaining account security</li>
<li>Only one account per individual/business is allowed</li>
</ul>

<h3>Purchase Terms</h3>
<ul>
<li>All orders are subject to acceptance</li>
<li>Prices may change without prior notice</li>
<li>Payment must be made before shipping</li>
<li>Return policy applies as per our guidelines (details available on Returns & Refunds page)</li>
</ul>

<h3>Intellectual Property</h3>
<p>All content on our website is owned by Kanha Fashion Hub and protected by copyright, trademark, and other intellectual property laws. You may not copy, reproduce, or distribute our content without prior written permission.</p>

<h3>Limitation of Liability</h3>
<p>To the maximum extent permitted by law, Kanha Fashion Hub shall not be liable for any indirect, incidental, special, or consequential damages arising from the use or inability to use our services.</p>

<h3>Governing Law</h3>
<p>These Terms shall be governed by and construed in accordance with the laws of India. Any disputes arising under these Terms shall fall under the exclusive jurisdiction of the courts in Madhya Pradesh, India.</p>';
    }

    private function getCookiePolicyContent(): string
    {
        return '<h2>Cookie Policy</h2>
<p>Understanding how we use cookies to improve your browsing and shopping experience.</p>

<h3>What Are Cookies?</h3>
<p>Cookies are small text files stored on your device when you visit our website. They help us deliver a smoother experience by remembering your preferences and analyzing how you interact with our site.</p>

<h3>Types of Cookies We Use</h3>
<h4>Essential Cookies:</h4>
<ul>
<li>Authentication and security</li>
<li>Shopping cart functionality</li>
<li>Session management</li>
<li>CSRF protection</li>
</ul>

<h4>Functional Cookies:</h4>
<ul>
<li>Language and region preferences</li>
<li>Currency selection</li>
<li>Recently viewed products</li>
</ul>

<h4>Analytics Cookies:</h4>
<ul>
<li>Page views and user journeys</li>
<li>Time spent on pages</li>
<li>Popular products and categories</li>
<li>Device and browser information</li>
</ul>

<h4>Marketing Cookies:</h4>
<ul>
<li>Personalized product recommendations</li>
<li>Retargeting advertisements</li>
<li>Social media integration</li>
<li>Conversion tracking</li>
</ul>

<h3>Third-Party Cookies</h3>
<p>We use services from trusted third parties to improve functionality:</p>
<ul>
<li><strong>Google Analytics:</strong> Website analytics</li>
<li><strong>Facebook Pixel:</strong> Social media marketing</li>
<li><strong>Razorpay:</strong> Secure payment processing</li>
</ul>

<h3>Managing Cookies</h3>
<p>You can control cookies through:</p>
<ul>
<li>Your browser settings</li>
<li>Our cookie consent banner</li>
<li>Third-party opt-out tools</li>
</ul>

<h3>Impact of Disabling Cookies</h3>
<p>Disabling certain cookies may affect your experience:</p>
<ul>
<li>You may need to re-enter information repeatedly</li>
<li>Some features may not function properly</li>
<li>Shopping cart and wishlist may not work correctly</li>
<li>Content and offers may be less relevant</li>
</ul>

<h3>Questions About Cookies</h3>
<p>If you have questions about our use of cookies, please contact us:</p>
<p><strong>Phone:</strong> <a href="tel:+91' . config('contact.phone') . '">+91-' . substr(config('contact.phone'), 0, 5) . '-' . substr(config('contact.phone'), 5) . '</a></p>';
    }
}
