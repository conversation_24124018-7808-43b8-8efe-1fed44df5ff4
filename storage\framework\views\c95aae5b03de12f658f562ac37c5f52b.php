<?php $__env->startSection('title', isset($category) ? $category->name . ' - Kanha Fashion Hub' : 'Collections - Kanha Fashion Hub'); ?>
<?php $__env->startSection('description', isset($category) ? $category->description : 'Browse our complete collection of exquisite jewelry
    including rings, necklaces, earrings, and bracelets.'); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="py-4 py-md-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <?php if(isset($category)): ?>
                        <h1 class="font-modern display-5 display-md-4 fw-bold mb-2 mb-md-3"><?php echo e($category->name); ?></h1>
                        <p class="lead text-muted d-none d-md-block"><?php echo e($category->description); ?></p>
                        <p class="text-muted d-md-none"><?php echo e(Str::limit($category->description, 80)); ?></p>
                    <?php elseif(isset($searchTerm)): ?>
                        <h1 class="font-modern display-5 display-md-4 fw-bold mb-2 mb-md-3">Search Results</h1>
                        <p class="lead text-muted d-none d-md-block">
                            <?php if($products->count() > 0): ?>
                                Found <?php echo e($products->total()); ?> results for "<?php echo e($searchTerm); ?>"
                            <?php else: ?>
                                No results found for "<?php echo e($searchTerm); ?>"
                            <?php endif; ?>
                        </p>
                        <p class="text-muted d-md-none">
                            <?php if($products->count() > 0): ?>
                                <?php echo e($products->total()); ?> results for "<?php echo e(Str::limit($searchTerm, 20)); ?>"
                            <?php else: ?>
                                No results found
                            <?php endif; ?>
                        </p>
                    <?php else: ?>
                        <h1 class="font-modern display-5 display-md-4 fw-bold mb-2 mb-md-3">Our Collections</h1>
                        <p class="lead text-muted d-none d-md-block">Discover our complete range of exquisite jewelry pieces
                        </p>
                        <p class="text-muted d-md-none">Exquisite jewelry pieces</p>
                    <?php endif; ?>

                    <!-- Breadcrumb -->
                    <nav aria-label="breadcrumb" class="d-none d-md-block">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="<?php echo e(url('/')); ?>">Home</a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('collections')); ?>">Collections</a></li>
                            <?php if(isset($category)): ?>
                                <?php if(isset($breadcrumb) && count($breadcrumb) > 1): ?>
                                    <?php $__currentLoopData = $breadcrumb; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $categoryItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($index < count($breadcrumb) - 1): ?>
                                            <li class="breadcrumb-item">
                                                <a href="<?php echo e(route('collections.category', $categoryItem->slug)); ?>"><?php echo e($categoryItem->name); ?></a>
                                            </li>
                                        <?php else: ?>
                                            <li class="breadcrumb-item active" aria-current="page"><?php echo e($categoryItem->name); ?></li>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <li class="breadcrumb-item active" aria-current="page"><?php echo e($category->name); ?></li>
                                <?php endif; ?>
                            <?php else: ?>
                                <li class="breadcrumb-item active" aria-current="page">Collections</li>
                            <?php endif; ?>
                        </ol>
                    </nav>

                    <!-- Mobile Breadcrumb -->
                    <nav aria-label="breadcrumb" class="d-md-none">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(url('/')); ?>"><i class="fas fa-home"></i></a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('collections')); ?>">Collections</a></li>
                            <?php if(isset($category)): ?>
                                <?php if(isset($breadcrumb) && count($breadcrumb) > 1): ?>
                                    <?php $__currentLoopData = $breadcrumb; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $categoryItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($index < count($breadcrumb) - 1): ?>
                                            <li class="breadcrumb-item">
                                                <a href="<?php echo e(route('collections.category', $categoryItem->slug)); ?>"><?php echo e(Str::limit($categoryItem->name, 10)); ?></a>
                                            </li>
                                        <?php else: ?>
                                            <li class="breadcrumb-item active" aria-current="page"><?php echo e(Str::limit($categoryItem->name, 15)); ?></li>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <li class="breadcrumb-item active" aria-current="page"><?php echo e(Str::limit($category->name, 15)); ?></li>
                                <?php endif; ?>
                            <?php else: ?>
                                <li class="breadcrumb-item active" aria-current="page">Collections</li>
                            <?php endif; ?>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Subcategory Navigation -->
    <?php if(isset($category) && $category->children && $category->children->count() > 0): ?>
    <section class="py-3 bg-light border-bottom">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h6 class="mb-3 text-center text-md-start">
                        <i class="fas fa-sitemap me-2"></i>Explore <?php echo e($category->name); ?> Subcategories
                    </h6>
                    <div class="d-flex flex-wrap justify-content-center justify-content-md-start gap-2">
                        <?php $__currentLoopData = $category->children->where('is_active', true); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e(route('collections.category', $child->slug)); ?>"
                               class="btn btn-outline-secondary btn-sm rounded-pill">
                                <i class="fas fa-arrow-right me-1"></i><?php echo e($child->name); ?>

                                <?php if($child->products()->active()->inStock()->count() > 0): ?>
                                    <span class="badge bg-primary ms-1"><?php echo e($child->products()->active()->inStock()->count()); ?></span>
                                <?php endif; ?>
                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Filter Section -->
    <section class="filter-section py-3 py-md-4 border-bottom sticky-top shadow-sm mb-4" style="top: var(--navbar-height); z-index: 1000; overflow: visible;">
        <div class="container" style="overflow: visible;">
            <!-- Unified Filter Layout -->
            <div style="overflow: visible;">
                <div class="row g-2 g-md-3 justify-content-center" style="overflow: visible;">
                    <!-- Category Filter -->
                    <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                        <div class="dropdown w-100">
                            <button class="btn btn-outline-pink btn-sm dropdown-toggle w-100" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-tags me-1"></i>
                                <?php if(isset($category)): ?>
                                    <span class="d-none d-md-inline"><?php echo e($category->name); ?></span>
                                    <span class="d-md-none"><?php echo e(Str::limit($category->name, 8)); ?></span>
                                <?php else: ?>
                                    Category
                                <?php endif; ?>
                            </button>
                            <ul class="dropdown-menu shadow-lg">
                                <li><a class="dropdown-item <?php echo e(!isset($category) ? 'active' : ''); ?>" href="<?php echo e(route('collections')); ?>">
                                    <i class="fas fa-list me-2"></i>All Categories
                                </a></li>
                                <?php if(isset($globalCategories)): ?>
                                    <?php $__currentLoopData = $globalCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><a class="dropdown-item <?php echo e(isset($category) && $category->slug == $cat->slug ? 'active' : ''); ?>"
                                                href="<?php echo e(route('collections.category', $cat->slug)); ?>">
                                            <i class="fas fa-tag me-2"></i><?php echo e($cat->name); ?>

                                        </a></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>

                    <!-- Price Range Filter -->
                    <div class="col-4">
                        <div class="dropdown w-100">
                            <button class="btn btn-outline-pink btn-sm dropdown-toggle w-100" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-rupee-sign me-1"></i>
                                <?php if(request('min_price') || request('max_price')): ?>
                                    <?php if(!request('min_price') && request('max_price') == 10000): ?>
                                        <₹10K
                                    <?php elseif(request('min_price') == 10000 && request('max_price') == 25000): ?>
                                        ₹10-25K
                                    <?php elseif(request('min_price') == 25000 && request('max_price') == 50000): ?>
                                        ₹25-50K
                                    <?php elseif(request('min_price') == 50000 && !request('max_price')): ?>
                                        >₹50K
                                    <?php else: ?>
                                        Custom
                                    <?php endif; ?>
                                <?php else: ?>
                                    Price
                                <?php endif; ?>
                            </button>
                            <ul class="dropdown-menu shadow-lg">
                                <li><a class="dropdown-item <?php echo e(!request('min_price') && !request('max_price') ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => null, 'max_price' => null])); ?>">
                                    <i class="fas fa-infinity me-2"></i>All Prices
                                </a></li>
                                <li><a class="dropdown-item <?php echo e(request('max_price') == 10000 && !request('min_price') ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => null, 'max_price' => 10000])); ?>">
                                    <i class="fas fa-coins me-2"></i>Under ₹10K
                                </a></li>
                                <li><a class="dropdown-item <?php echo e(request('min_price') == 10000 && request('max_price') == 25000 ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => 10000, 'max_price' => 25000])); ?>">
                                    <i class="fas fa-money-bill me-2"></i>₹10K - ₹25K
                                </a></li>
                                <li><a class="dropdown-item <?php echo e(request('min_price') == 25000 && request('max_price') == 50000 ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => 25000, 'max_price' => 50000])); ?>">
                                    <i class="fas fa-money-bill-wave me-2"></i>₹25K - ₹50K
                                </a></li>
                                <li><a class="dropdown-item <?php echo e(request('min_price') == 50000 && !request('max_price') ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['min_price' => 50000, 'max_price' => null])); ?>">
                                    <i class="fas fa-gem me-2"></i>Above ₹50K
                                </a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Sort Options -->
                    <div class="col-4">
                        <div class="dropdown w-100">
                            <button class="btn btn-outline-pink btn-sm dropdown-toggle w-100" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-sort me-1"></i>
                                <?php if(request('sort') == 'price_low'): ?>
                                    Price ↑
                                <?php elseif(request('sort') == 'price_high'): ?>
                                    Price ↓
                                <?php elseif(request('sort') == 'newest'): ?>
                                    Newest
                                <?php elseif(request('sort') == 'name'): ?>
                                    A-Z
                                <?php elseif(request('sort') == 'popular'): ?>
                                    Popular
                                <?php elseif(request('sort') == 'rating'): ?>
                                    Rating
                                <?php elseif(request('sort') == 'discount'): ?>
                                    Discount
                                <?php else: ?>
                                    Featured
                                <?php endif; ?>
                            </button>
                            <ul class="dropdown-menu shadow-lg">
                                <li><a class="dropdown-item <?php echo e(request('sort') == 'featured' || !request('sort') ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['sort' => 'featured'])); ?>">
                                    <i class="fas fa-star me-2"></i>Featured
                                </a></li>
                                <li><a class="dropdown-item <?php echo e(request('sort') == 'price_low' ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['sort' => 'price_low'])); ?>">
                                    <i class="fas fa-sort-amount-up me-2"></i>Price ↑
                                </a></li>
                                <li><a class="dropdown-item <?php echo e(request('sort') == 'price_high' ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['sort' => 'price_high'])); ?>">
                                    <i class="fas fa-sort-amount-down me-2"></i>Price ↓
                                </a></li>
                                <li><a class="dropdown-item <?php echo e(request('sort') == 'newest' ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['sort' => 'newest'])); ?>">
                                    <i class="fas fa-clock me-2"></i>Newest
                                </a></li>
                                <li><a class="dropdown-item <?php echo e(request('sort') == 'name' ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['sort' => 'name'])); ?>">
                                    <i class="fas fa-sort-alpha-down me-2"></i>A-Z
                                </a></li>
                                <li><a class="dropdown-item <?php echo e(request('sort') == 'popular' ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['sort' => 'popular'])); ?>">
                                    <i class="fas fa-fire me-2"></i>Popular
                                </a></li>
                                <li><a class="dropdown-item <?php echo e(request('sort') == 'rating' ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['sort' => 'rating'])); ?>">
                                    <i class="fas fa-star-half-alt me-2"></i>Rating
                                </a></li>
                                <li><a class="dropdown-item <?php echo e(request('sort') == 'discount' ? 'active' : ''); ?>"
                                        href="<?php echo e(request()->fullUrlWithQuery(['sort' => 'discount'])); ?>">
                                    <i class="fas fa-percentage me-2"></i>Discount
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>



        </div>
    </section>

    <!-- Products Grid -->
    <section class="py-5">
        <div class="container">
            <?php if($products->count() > 0): ?>
                <div class="row g-4 g-md-4">
                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-12 col-md-6 col-lg-4 col-xl-3">
                            <div class="card product-card h-100 <?php echo e(!$product->in_stock ? 'out-of-stock' : ''); ?>">
                                <div class="position-relative overflow-hidden product-image-container">
                                    <?php if($product->images && count($product->images) > 0): ?>
                                        <?php
                                            $imagePath = $product->images[0];
                                            // Check if it's a full URL
                                            if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
                                                $imageUrl = $imagePath;
                                            } else {
                                                // Clean the path and ensure storage/ prefix
                                                $cleanPath = str_replace(['\\/', '\\'], '/', $imagePath);
                                                // Add storage/ prefix if not already present
                                                if (!str_starts_with($cleanPath, 'storage/')) {
                                                    $cleanPath = 'storage/' . ltrim($cleanPath, '/');
                                                }
                                                $imageUrl = asset($cleanPath);
                                            }
                                        ?>
                                        <img src="<?php echo e($imageUrl); ?>"
                                             class="card-img-top <?php echo e(!$product->in_stock ? 'opacity-50' : ''); ?>"
                                             alt="<?php echo e($product->name); ?>"
                                             style="height: 250px; object-fit: cover;"
                                             onerror="this.src='https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'">
                                    <?php else: ?>
                                        <img src="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                                             class="card-img-top <?php echo e(!$product->in_stock ? 'opacity-50' : ''); ?>"
                                             alt="<?php echo e($product->name); ?>"
                                             style="height: 250px; object-fit: cover;">
                                    <?php endif; ?>

                                    <?php if(!$product->in_stock): ?>
                                        <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.3);">
                                            <span class="badge bg-secondary fs-6 px-3 py-2">
                                                <i class="fas fa-times me-1"></i>Out of Stock
                                            </span>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Hover hint for desktop -->
                                    <!-- Removed overlay - keeping only subtle hover effects -->

                                    <?php if($product->isOnSale()): ?>
                                        <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                                            <?php echo e($product->discount_percentage); ?>% OFF
                                        </span>
                                    <?php elseif($product->is_featured): ?>
                                        <span
                                            class="badge position-absolute top-0 start-0 m-2" style="background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink)); color: white;">Featured</span>
                                    <?php endif; ?>
                                </div>
                                <div class="card-body text-center d-flex flex-column">
                                    <h5 class="card-title font-modern"><?php echo e($product->name); ?></h5>
                                    <p class="card-text text-muted small"><?php echo e($product->category->name); ?></p>
                                    <div class="d-flex justify-content-center align-items-center gap-2 mb-2">
                                        <?php if($product->isOnSale()): ?>
                                            <span class="fw-bold fs-5 font-modern product-price"
                                                  style="color: var(--primary-pink);"
                                                  data-base-price="<?php echo e($product->sale_price); ?>"
                                                  data-original-price="<?php echo e($product->price); ?>">₹<?php echo e(number_format($product->sale_price)); ?></span>
                                            <span class="text-muted text-decoration-line-through small original-price">₹<?php echo e(number_format($product->price)); ?></span>
                                        <?php else: ?>
                                            <span class="fw-bold fs-5 font-modern product-price"
                                                  style="color: var(--primary-pink);"
                                                  data-base-price="<?php echo e($product->price); ?>">₹<?php echo e(number_format($product->price)); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <?php if($product->metal_type || $product->stone_type): ?>
                                        <div class="mt-auto mb-3">
                                            <?php if($product->metal_type): ?>
                                                <small
                                                    class="badge bg-light text-dark me-1"><?php echo e($product->metal_type); ?></small>
                                            <?php endif; ?>
                                            <?php if($product->stone_type): ?>
                                                <small class="badge bg-light text-dark"><?php echo e($product->stone_type); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Size Selection for products with sizes -->
                                    <?php
                                        $availableSizes = $product->getAvailableSizesWithStock();
                                    ?>
                                    <?php if($availableSizes && count($availableSizes) > 0): ?>
                                        <div class="mb-3">
                                            <small class="text-muted d-block mb-1">Size:</small>
                                            <div class="d-flex gap-1 flex-wrap justify-content-center size-options" data-product-id="<?php echo e($product->id); ?>">
                                                <?php $__currentLoopData = $availableSizes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sizeInfo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php if($sizeInfo['is_available'] && $sizeInfo['stock_quantity'] > 0): ?>
                                                        <button type="button"
                                                                class="btn btn-outline-secondary btn-sm size-option"
                                                                data-size-id="<?php echo e($sizeInfo['size_id']); ?>"
                                                                data-price-adjustment="<?php echo e($sizeInfo['price_adjustment']); ?>"
                                                                data-size-name="<?php echo e($sizeInfo['name']); ?>"
                                                                style="min-width: 35px; font-size: 0.75rem;">
                                                            <?php echo e($sizeInfo['name']); ?>

                                                        </button>
                                                    <?php endif; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Clear Add to Cart Button for All Devices -->
                                    <div class="mt-auto">
                                        <?php if($product->in_stock): ?>
                                            <?php if($availableSizes && count($availableSizes) > 0): ?>
                                                <button class="btn btn-primary-pink w-100 mb-2 add-to-cart-btn"
                                                    data-product-id="<?php echo e($product->id); ?>"
                                                    data-requires-size="true"
                                                    style="font-weight: 600;" disabled>
                                                    <i class="fas fa-shopping-cart me-2"></i>Select Size First
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-primary-pink w-100 mb-2 add-to-cart-btn"
                                                    data-product-id="<?php echo e($product->id); ?>"
                                                    style="font-weight: 600;">
                                                    <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                                                </button>
                                            <?php endif; ?>
                                            <div class="d-flex gap-2">
                                                <a href="<?php echo e(route('product.detail', $product->slug)); ?>"
                                                   class="btn btn-outline-primary btn-sm flex-fill">
                                                    <i class="fas fa-eye me-1"></i>View
                                                </a>
                                            </div>
                                        <?php else: ?>
                                            <button class="btn btn-secondary w-100 mb-2" disabled>
                                                <i class="fas fa-times me-2"></i>Out of Stock
                                            </button>
                                            <div class="d-flex gap-2">
                                                <a href="<?php echo e(route('product.detail', $product->slug)); ?>"
                                                   class="btn btn-outline-primary btn-sm flex-fill">
                                                    <i class="fas fa-eye me-1"></i>View
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>


                <!-- Pagination -->
                <?php if($products->hasPages()): ?>
                    <div class="row mt-5">
                        <div class="col-12">
                            <nav aria-label="Products pagination">
                                <?php echo e($products->appends(request()->query())->links('pagination::bootstrap-5')); ?>

                            </nav>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <!-- No Products Found -->
                <div class="row">
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-gem text-muted mb-4" style="font-size: 4rem;"></i>
                        <h3 class="font-modern mb-3">No Products Found</h3>
                        <p class="text-muted mb-4">
                            <?php if(isset($category)): ?>
                                No products found in <?php echo e($category->name); ?> category.
                            <?php elseif(isset($searchTerm)): ?>
                                No products found for "<?php echo e($searchTerm); ?>".
                            <?php else: ?>
                                No products found matching your criteria.
                            <?php endif; ?>
                        </p>
                        <a href="<?php echo e(route('collections')); ?>" class="btn btn-primary-pink">
                            <i class="fas fa-arrow-left me-2"></i>View All Products
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// ===== BOOTSTRAP DROPDOWN INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    // Use Bootstrap's native dropdown functionality for better mobile support
    const dropdownElementList = [].slice.call(document.querySelectorAll('.filter-section .dropdown-toggle'));
    const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
        // Ensure Bootstrap dropdown attributes are set
        dropdownToggleEl.setAttribute('data-bs-toggle', 'dropdown');
        dropdownToggleEl.setAttribute('aria-expanded', 'false');

        return new bootstrap.Dropdown(dropdownToggleEl, {
            autoClose: true,
            boundary: 'viewport'
        });
    });

    // Add touch-friendly behavior for mobile
    if ('ontouchstart' in window) {
        dropdownElementList.forEach(function(dropdownToggle) {
            dropdownToggle.addEventListener('touchstart', function(e) {
                // Ensure the dropdown can be opened on touch devices
                const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this);
                if (!this.getAttribute('aria-expanded') || this.getAttribute('aria-expanded') === 'false') {
                    e.preventDefault();
                    dropdown.show();
                }
            });
        });
    }
});
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
    <style>
        .breadcrumb-item+.breadcrumb-item::before {
            color: var(--primary-pink);
        }

        .breadcrumb-item a {
            color: var(--primary-pink);
            text-decoration: none;
        }

        .breadcrumb-item a:hover {
            text-decoration: underline;
        }

        .breadcrumb-item.active {
            color: var(--primary-pink);
            font-weight: 600;
        }

        .pagination .page-link {
            color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink));
            border-color: var(--primary-pink);
            color: white;
        }

        .pagination .page-link:hover {
            background: linear-gradient(135deg, var(--secondary-pink), var(--primary-pink));
            border-color: var(--secondary-pink);
            color: white;
        }

        /* ===== CLEAN FILTER SECTION STYLING ===== */

        /* Remove conflicting button styles - use global styles from app.blade.php */

        /* Form Select Styling */
        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23E91E63' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
            transition: all 0.3s ease;
            border-color: var(--primary-pink);
        }

        .form-select:focus {
            border-color: var(--primary-pink);
            box-shadow: 0 0 0 0.2rem rgba(233, 30, 99, 0.25);
        }

        .form-select:hover {
            border-color: var(--secondary-pink);
            box-shadow: 0 2px 8px rgba(233, 30, 99, 0.15);
        }

        /* Dropdown Menu - Fixed Positioning and Z-Index */
        .dropdown-menu {
            border: 2px solid rgba(233, 30, 99, 0.2) !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
            padding: 0.5rem 0 !important;
            margin-top: 0.5rem !important;
            background: white !important;
            backdrop-filter: blur(10px) !important;
            min-width: 200px !important;
            z-index: 9999 !important;
            position: absolute !important;
            top: 100% !important;
            left: 0 !important;
            display: none !important;
            opacity: 0 !important;
            transform: translateY(-10px) !important;
            transition: all 0.3s ease !important;
        }

        .dropdown-menu.show {
            display: block !important;
            opacity: 1 !important;
            transform: translateY(0) !important;
        }

        /* Dropdown Items - No Hover Hide Issues */
        .dropdown-item {
            padding: 0.75rem 1rem !important;
            transition: background-color 0.2s ease, color 0.2s ease !important;
            border-radius: 8px !important;
            margin: 2px 8px !important;
            display: flex !important;
            align-items: center !important;
            color: #495057 !important;
            font-weight: 500 !important;
            text-decoration: none !important;
            white-space: nowrap !important;
        }

        .dropdown-item:hover,
        .dropdown-item:focus {
            background-color: rgba(233, 30, 99, 0.1) !important;
            color: var(--primary-pink) !important;
            text-decoration: none !important;
        }

        .dropdown-item.active {
            background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink)) !important;
            color: white !important;
        }

        /* Dropdown Toggle Animation */
        .dropdown-toggle::after {
            transition: transform 0.3s ease;
        }

        .dropdown.show .dropdown-toggle::after {
            transform: rotate(180deg);
        }

        /* ===== PREVENT LAYOUT SHIFTS AND SCROLLBARS ===== */

        /* Ensure dropdowns appear above everything */
        .dropdown {
            position: relative !important;
            z-index: 1000 !important;
        }

        /* Prevent dropdown from being hidden */
        .filter-section {
            position: relative !important;
            z-index: 1000 !important;
            overflow: visible !important;
        }

        .filter-section .container {
            overflow: visible !important;
        }

        .filter-section .row {
            overflow: visible !important;
        }

        .filter-section .col-12,
        .filter-section .col-4,
        .filter-section .col-sm-6,
        .filter-section .col-md-4,
        .filter-section .col-lg-3 {
            overflow: visible !important;
        }

        /* ===== SIMPLE CLICK-ONLY DROPDOWN STYLES ===== */

        /* Hide all dropdown menus by default */
        .filter-section .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            z-index: 9999;
            background: white;
            border: 2px solid rgba(233, 30, 99, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            padding: 0.5rem 0;
            margin-top: 0.5rem;
            min-width: 200px;
        }

        /* Show dropdown when .show class is added */
        .filter-section .dropdown.show .dropdown-menu {
            display: block;
        }

        /* NO hover effects at all */
        .filter-section .dropdown:hover .dropdown-menu {
            display: none;
        }

        /* Button hover styles only */
        .filter-section .btn-outline-pink:hover {
            color: white;
            border-color: var(--primary-pink);
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.25);
        }

        /* Dropdown items styling */
        .filter-section .dropdown-item {
            padding: 0.75rem 1rem;
            color: #495057;
            font-weight: 500;
            border-radius: 8px;
            margin: 2px 8px;
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .filter-section .dropdown-item:hover {
            background-color: rgba(233, 30, 99, 0.1);
            color: var(--primary-pink);
            text-decoration: none;
        }

        .filter-section .dropdown-item.active {
            background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink));
            color: white;
        }

        /* Filter Section Background and Spacing */
        .filter-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 187, 217, 0.05));
            backdrop-filter: blur(10px);
            margin-bottom: 2rem !important;
        }

        /* ===== FORCE DROPDOWN VISIBILITY ===== */

        /* Override any Bootstrap hiding */
        .dropdown-menu[data-bs-popper] {
            position: absolute !important;
            inset: 0px auto auto 0px !important;
            margin: 0px !important;
            transform: translate(0px, 42px) !important;
        }

        /* Ensure dropdowns appear above all content */
        .dropdown-menu {
            position: absolute !important;
            z-index: 99999 !important;
            will-change: transform !important;
            pointer-events: auto !important;
        }

        /* Force visibility when shown */
        .dropdown.show .dropdown-menu {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            pointer-events: auto !important;
        }

        /* Prevent any parent from hiding dropdowns */
        .filter-section,
        .filter-section *,
        .container,
        .row,
        .col-12,
        .col-4,
        .col-sm-6,
        .col-md-4,
        .col-lg-3 {
            overflow: visible !important;
        }

        /* Debug: Make dropdown containers visible */
        .dropdown {
            position: relative !important;
            overflow: visible !important;
        }

        /* Ensure dropdown items are clickable */
        .dropdown-item {
            pointer-events: auto !important;
            cursor: pointer !important;
        }

        /* ===== RESPONSIVE DESIGN ===== */

        /* Mobile Filter Improvements */
        @media (max-width: 767.98px) {
            .btn-outline-pink.btn-sm {
                font-size: 0.8rem;
                padding: 0.4rem 0.6rem;
                min-height: 44px; /* Touch-friendly */
            }

            .form-select.form-select-sm {
                font-size: 0.8rem;
                padding: 0.4rem 0.6rem;
                min-height: 44px; /* Touch-friendly */
            }

            .dropdown-menu {
                min-width: 150px !important;
                font-size: 0.9rem;
            }

            .dropdown-item {
                padding: 0.75rem 1rem !important;
                font-size: 0.9rem;
            }
        }

        /* Desktop Filter Improvements */
        @media (min-width: 768px) {
            .btn-outline-pink {
                min-width: 130px;
            }

            .form-select {
                min-width: 200px;
            }

            .dropdown-menu {
                min-width: 200px !important;
            }
        }
            transform: translateX(5px);
        }

        .dropdown-item.active {
            background-color: var(--primary-pink) !important;
            color: white !important;
            font-weight: 600;
        }

        .dropdown-item i {
            width: 20px;
            text-align: center;
        }

        /* Filter Button Enhancements */
        /* Removed duplicate .btn-outline-pink styles to prevent flickering */

        /* Mobile Filter Button Spacing */
        @media (max-width: 767.98px) {
            .btn-sm {
                font-size: 0.75rem;
                padding: 0.375rem 0.5rem;
            }

            .dropdown-menu {
                min-width: 180px;
                font-size: 0.9rem;
            }
        }

        /* Desktop Filter Button Spacing - removed conflicting min-width */

        .product-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            overflow: hidden;
            background: #fff;
            height: 100%;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
        }

        .product-image-container {
            height: 250px;
            overflow: hidden;
            position: relative;
            background: #f8f9fa;
            border-radius: 12px 12px 0 0;
        }

        .card-img-top {
            height: 100%;
            width: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .card-img-top {
            transform: scale(1.05);
        }

        /* Hover hint styling */
        .hover-hint {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .product-card:hover .hover-hint {
            opacity: 0;
        }

        /* Removed product overlay - keeping only subtle hover effects */

        /* Mobile-First Collections Page Improvements */

        /* Mobile Header Adjustments */
        @media (max-width: 767.98px) {
            .display-5 {
                font-size: 2rem !important;
            }

            .breadcrumb {
                font-size: 0.85rem;
            }

            .breadcrumb-item+.breadcrumb-item::before {
                font-size: 0.75rem;
            }
        }

        /* Mobile Filter Section */
        .sticky-top {
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 767.98px) {
            .btn-sm {
                font-size: 0.8rem;
                padding: 0.375rem 0.5rem;
            }

            .form-select-sm {
                font-size: 0.8rem;
                padding: 0.375rem 0.5rem;
            }
        }

        /* Mobile Product Grid - Vertical Layout */
        @media (max-width: 575.98px) {
            .product-card {
                border-radius: 16px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
                overflow: hidden;
            }

            .product-card:hover {
                transform: translateY(-4px);
                box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            }

            .product-image-container {
                height: auto !important;
                aspect-ratio: 1/1;
                min-height: 0;
            }
            .product-image-container img.card-img-top {
                width: 100% !important;
                height: 100% !important;
                object-fit: cover !important;
                display: block;
            }

            .card-body {
                padding: 1rem;
                text-align: center;
            }

            .card-title {
                font-size: 1.1rem;
                margin-bottom: 0.5rem;
                line-height: 1.3;
                font-weight: 600;
                color: #2c3e50;
            }

            .card-text {
                font-size: 0.85rem;
                margin-bottom: 0.75rem;
                color: #6c757d;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .fs-5 {
                font-size: 1.25rem !important;
                font-weight: 700;
                color: var(--primary-pink);
            }

            .badge {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
                border-radius: 20px;
                margin: 0.125rem;
            }

            /* Mobile action buttons */
            .mobile-add-cart-btn,
            .buy-now-btn-mobile {
                font-size: 0.9rem;
                padding: 0.75rem 1rem;
                border-radius: 25px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .mobile-add-cart-btn {
                background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink));
                border: none;
                color: white;
            }

            .buy-now-btn-mobile {
                background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
                border: none;
                color: var(--text-dark);
            }

            /* Product overlay removed */

            /* Adjust spacing */
            .row.g-4 {
                --bs-gutter-y: 1.5rem;
            }

            /* Enhanced typography */
            .card-title {
                font-family: 'Playfair Display', serif;
                font-weight: 600;
                color: #2c3e50;
                line-height: 1.3;
            }

            .card-text {
                color: #6c757d;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                font-weight: 500;
            }

            /* Enhanced price styling */
            .text-primary-brown {
                color: var(--primary-brown) !important;
                font-weight: 700;
            }

            /* Mobile-specific badge positioning */
            .badge.position-absolute {
                top: 1rem !important;
                left: 1rem !important;
                z-index: 2;
                font-weight: 600;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }

            /* Price section styling */
            .d-flex.justify-content-center.justify-content-sm-start {
                justify-content: center !important;
                margin-bottom: 1rem;
            }

            /* Badge container styling */
            .mt-auto {
                margin-top: 0.75rem !important;
            }

            /* Mobile button container */
            .d-flex.gap-2.mt-3.d-md-none {
                margin-top: 1rem !important;
                gap: 0.75rem !important;
            }

            /* Enhanced mobile typography */
            .font-modern {
                font-family: 'Poppins', 'Inter', sans-serif;
                font-weight: 600;
            }

            /* Better mobile card spacing */
            .container .row.g-3.g-md-4 {
                margin: 0 -0.5rem;
            }

            .container .row.g-3.g-md-4 > .col-12 {
                padding: 0 0.5rem;
            }
        }

        /* Tablet Product Grid */
        @media (min-width: 576px) and (max-width: 767.98px) {
            .product-card {
                border-radius: 12px;
                box-shadow: 0 3px 10px rgba(0,0,0,0.1);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }

            .product-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            }

            .product-image-container {
                height: 220px;
                border-radius: 12px 12px 0 0;
                overflow: hidden;
            }

            .product-image-container img {
                transition: transform 0.3s ease;
            }

            .product-card:hover .product-image-container img {
                transform: scale(1.03);
            }

            .card-body {
                padding: 1rem;
            }

            .card-title {
                font-size: 1.05rem;
                font-weight: 600;
                margin-bottom: 0.5rem;
            }

            .card-text {
                font-size: 0.9rem;
                margin-bottom: 0.75rem;
            }

            .fs-5 {
                font-size: 1.15rem !important;
                font-weight: 700;
            }
        }

        /* Overlay removed */

        /* Desktop Product Grid */
        @media (min-width: 768px) {
            .product-card {
                border-radius: 12px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.08);
                transition: all 0.3s ease;
                overflow: hidden;
            }

            .product-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            }

            .product-image-container {
                height: 280px;
                border-radius: 12px 12px 0 0;
                overflow: hidden;
                position: relative;
            }

            .product-image-container img {
                transition: transform 0.4s ease;
            }

            .product-card:hover .product-image-container img {
                transform: scale(1.08);
            }

            .card-body {
                padding: 1.25rem;
            }

            .card-title {
                font-size: 1.15rem;
                font-weight: 600;
                margin-bottom: 0.5rem;
                color: #2c3e50;
            }

            .card-text {
                font-size: 0.9rem;
                margin-bottom: 0.75rem;
                color: #6c757d;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .fs-5 {
                font-size: 1.3rem !important;
                font-weight: 700;
                color: var(--primary-pink);
            }

            /* Removed desktop overlay styles */

            /* Badge styling for desktop */
            .badge.position-absolute {
                top: 1rem;
                left: 1rem;
                font-weight: 600;
                border-radius: 20px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }

            .mobile-add-cart-btn,
            .buy-now-btn-mobile {
                display: none !important;
            }
        }

        /* Size selection styles */
        .size-option {
            transition: all 0.2s ease;
            border-radius: 4px !important;
        }

        .size-option:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .size-option.active {
            background: var(--primary-pink) !important;
            border-color: var(--primary-pink) !important;
            color: white !important;
        }

        /* Mobile pagination */
        @media (max-width: 767.98px) {
            .pagination {
                justify-content: center;
            }

            .page-link {
                padding: 0.5rem 0.75rem;
                font-size: 0.9rem;
            }
        }

        /* Mobile empty state */
        @media (max-width: 767.98px) {
            .py-5 {
                padding: 2rem 0 !important;
            }

            .text-center h3 {
                font-size: 1.5rem;
            }

            .text-center p {
                font-size: 0.9rem;
            }
        }

        /* Touch-friendly improvements */
        @media (max-width: 767.98px) {

            .dropdown-toggle,
            .form-select,
            .btn {
                min-height: 44px;
            }

            .dropdown-item {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }
        }

        /* Performance optimizations for mobile */
        @media (max-width: 767.98px) {
            .product-card .card-img-top {
                will-change: auto;
            }

            .product-card:hover .card-img-top {
                transform: none;
            }
        }

        /* Out of stock styling */
        .product-card.out-of-stock {
            position: relative;
        }

        .product-card.out-of-stock .card-body {
            opacity: 0.7;
        }

        /* Overlay removed */

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {

            .product-card,
            .product-card:hover {
                transform: none;
                transition: none;
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        // Size selection functionality
        document.querySelectorAll('.size-option').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.closest('.size-options').dataset.productId;
                const sizeId = this.dataset.sizeId;
                const priceAdjustment = parseFloat(this.dataset.priceAdjustment) || 0;

                // Remove active class from other size options for this product
                this.closest('.size-options').querySelectorAll('.size-option').forEach(btn => {
                    btn.classList.remove('active');
                    btn.classList.remove('btn-primary');
                    btn.classList.add('btn-outline-secondary');
                });

                // Add active class to selected size
                this.classList.add('active');
                this.classList.remove('btn-outline-secondary');
                this.classList.add('btn-primary');

                // Update price display
                const productCard = this.closest('.product-card');
                const priceElement = productCard.querySelector('.product-price');
                const basePrice = parseFloat(priceElement.dataset.basePrice);
                const newPrice = basePrice + priceAdjustment;

                // Update displayed price
                priceElement.textContent = '₹' + new Intl.NumberFormat('en-IN').format(newPrice);

                // Enable add to cart button
                const addToCartBtn = productCard.querySelector('.add-to-cart-btn');
                if (addToCartBtn.dataset.requiresSize === 'true') {
                    addToCartBtn.disabled = false;
                    addToCartBtn.innerHTML = '<i class="fas fa-shopping-cart me-2"></i>Add to Cart';
                    addToCartBtn.dataset.selectedSizeId = sizeId;
                }
            });
        });

        // Add to cart functionality
        document.querySelectorAll('.add-to-cart-btn').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;
                const requiresSize = this.dataset.requiresSize === 'true';
                const selectedSizeId = this.dataset.selectedSizeId;

                // Check if size is required but not selected
                if (requiresSize && !selectedSizeId) {
                    showNotification('Please select a size first', 'error');
                    return;
                }

                const requestData = {
                    product_id: productId,
                    quantity: 1
                };

                if (selectedSizeId) {
                    requestData.size_id = selectedSizeId;
                }

                fetch('/cart/add', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                .getAttribute('content'),
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify(requestData)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            showNotification('Product added to cart!', 'success');

                            // Update cart count if available
                            if (data.cart_count) {
                                updateCartCount(data.cart_count);
                            }
                        } else {
                            let errorMessage = data.message || 'Error adding product to cart';
                            if (errorMessage.includes('out of stock') || errorMessage.includes('insufficient')) {
                                errorMessage = 'Sorry, this product is currently out of stock or has insufficient quantity available.';
                            }
                            showNotification(errorMessage, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('Unable to add product to cart. Please check your connection and try again.', 'error');
                    });
            });
        });

        // Buy Now functionality
        document.querySelectorAll('.buy-now-btn, .buy-now-btn-mobile').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;

                // Add loading state
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
                this.disabled = true;

                fetch('/cart/add', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                .getAttribute('content'),
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            product_id: productId,
                            quantity: 1,
                            buy_now: true
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Redirect to checkout
                            window.location.href = '/checkout';
                        } else {
                            // Restore button state
                            this.innerHTML = originalText;
                            this.disabled = false;
                            showNotification(data.message || 'Error processing request', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        // Restore button state
                        this.innerHTML = originalText;
                        this.disabled = false;
                        showNotification('Error processing request', 'error');
                    });
            });
        });

        // Add to cart functionality for mobile buttons
        document.querySelectorAll('.add-to-cart-btn-mobile').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;

                fetch('/cart/add', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                .getAttribute('content'),
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            product_id: productId,
                            quantity: 1
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showNotification('Product added to cart!', 'success');
                            if (data.cart_count) {
                                updateCartCount(data.cart_count);
                            }
                        } else {
                            showNotification(data.message || 'Error adding product to cart', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('Error adding product to cart', 'error');
                    });
            });
        });

        // Utility functions
        function showNotification(message, type) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className =
                `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px; max-width: 350px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
            notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

            document.body.appendChild(notification);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        function updateCartCount(count) {
            const cartCountElements = document.querySelectorAll('.cart-count');
            cartCountElements.forEach(element => {
                element.textContent = count;
            });
        }

        // New Add to Cart function for mobile buttons
        function addToCartMobile(productId) {
            fetch('/cart/add', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: 1
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to cart!', 'success');
                    if (data.cart_count) {
                        updateCartCount(data.cart_count);
                    }
                } else {
                    let errorMessage = data.message || 'Error adding product to cart';
                    if (errorMessage.includes('out of stock') || errorMessage.includes('insufficient')) {
                        errorMessage = 'Sorry, this product is currently out of stock or has insufficient quantity available.';
                    }
                    showNotification(errorMessage, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Unable to add product to cart. Please check your connection and try again.', 'error');
            });
        }

        // Additional cart functionality and notifications are handled above
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\kanha-shop-web\resources\views/collections.blade.php ENDPATH**/ ?>