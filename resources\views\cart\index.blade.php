@extends('layouts.app')

@section('title', 'Shopping Cart - Kanha Fashion Hub')
@section('description', 'Review your selected fashion jewelry items and proceed to checkout.')

@section('content')
    <!-- Modern Mobile-First Cart Design -->
    <div class="modern-cart">
        <!-- Header Section -->
        <div class="cart-header bg-white sticky-top shadow-sm">
            <div class="container-fluid px-3 px-md-4">
                <div class="d-flex align-items-center justify-content-between py-3">
                    <div class="d-flex align-items-center">
                        <a href="{{ route('collections') }}" class="btn btn-link p-0 me-3 text-dark">
                            <i class="fas fa-arrow-left fs-5"></i>
                        </a>
                        <div>
                            <h1 class="h4 mb-0 fw-bold">Shopping Cart</h1>
                            @if ($cartItems && $cartItems->count() > 0)
                                <small class="text-muted">{{ $cartItems->sum('quantity') }} items</small>
                            @endif
                        </div>
                    </div>
                    @if ($cartItems && $cartItems->count() > 0)
                        <button class="btn btn-outline-danger btn-sm" onclick="clearCart()">
                            <i class="fas fa-trash me-1"></i>Clear
                        </button>
                    @endif
                </div>
            </div>
        </div>

        @if ($cartItems && $cartItems->count() > 0)
            <!-- Cart Content -->
            <div class="cart-content">
                <div class="container-fluid px-3 px-md-4">
                    <!-- Mobile-First Layout -->
                    <div class="row g-4">
                        <!-- Cart Items - Full width on mobile, 8 cols on desktop -->
                        <div class="col-12 col-lg-8">
                            <div class="cart-items-section">
                                @foreach ($cartItems as $item)
                                    <div class="cart-item-modern mb-3" id="cart-item-{{ $item->id }}">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-body p-3">
                                                <div class="row g-3 align-items-center">
                                                    <!-- Product Image -->
                                                    <div class="col-3 col-sm-2">
                                                        <div class="product-image-container">
                                                            <a href="{{ route('product.detail', $item->product->slug) }}">
                                                                <img src="{{ $item->product->main_image_url }}"
                                                                    alt="{{ $item->product->name }}"
                                                                    class="img-fluid rounded">
                                                            </a>
                                                        </div>
                                                    </div>

                                                    <!-- Product Details -->
                                                    <div class="col-9 col-sm-10">
                                                        <div class="product-details">
                                                            <h6 class="product-name mb-1 fw-bold">
                                                                <a href="{{ route('product.detail', $item->product->slug) }}"
                                                                    class="text-decoration-none text-dark">
                                                                    {{ $item->product->name }}
                                                                </a>
                                                            </h6>
                                                            <div class="product-meta mb-2">
                                                                <small
                                                                    class="text-muted">{{ $item->product->category->name }}</small>
                                                                @if ($item->size_id && $item->size)
                                                                    <span class="text-muted mx-1">•</span>
                                                                    <small class="text-muted">Size:
                                                                        <strong>{{ $item->size->full_display_name }}</strong></small>
                                                                @endif
                                                            </div>

                                                            <!-- Mobile: Price and Quantity in same row -->
                                                            <div class="d-flex align-items-center justify-content-between">
                                                                <div class="price-section">
                                                                    <span
                                                                        class="h6 text-primary fw-bold mb-0">₹{{ number_format($item->total_price) }}</span>
                                                                    @if ($item->quantity > 1)
                                                                        <small
                                                                            class="text-muted d-block">₹{{ number_format($item->total_price / $item->quantity) }}
                                                                            each</small>
                                                                    @endif
                                                                </div>

                                                                <div class="quantity-controls d-flex align-items-center">
                                                                    <button class="btn btn-sm btn-outline-secondary"
                                                                        type="button"
                                                                        onclick="updateQuantity({{ $item->id }}, -1)">
                                                                        <i class="fas fa-minus"></i>
                                                                    </button>
                                                                    <span class="mx-3 fw-bold">{{ $item->quantity }}</span>
                                                                    <button class="btn btn-sm btn-outline-secondary"
                                                                        type="button"
                                                                        onclick="updateQuantity({{ $item->id }}, 1)">
                                                                        <i class="fas fa-plus"></i>
                                                                    </button>
                                                                    <button class="btn btn-sm btn-outline-danger ms-2"
                                                                        type="button"
                                                                        onclick="removeFromCart({{ $item->id }})">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Cart Summary - Separate Column -->
                        <div class="col-12 col-lg-4">
                            <div class="cart-summary-modern">
                                <!-- Desktop: Normal position, Mobile: Sticky bottom -->
                                <div class="d-none d-lg-block">
                                    <div class="card border-0 shadow-sm sticky-lg-top" style="top: 20px;">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0">Order Summary</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="summary-row d-flex justify-content-between mb-2">
                                                <span>Subtotal ({{ $cartItems->sum('quantity') }} items)</span>
                                                <span class="fw-bold">₹{{ number_format($subtotal) }}</span>
                                            </div>

                                            <div class="summary-row d-flex justify-content-between mb-2">
                                                <span>
                                                    Shipping
                                                    @if ($freeShippingThreshold > 0)
                                                        <small class="d-block text-muted">(Free above ₹{{ number_format($freeShippingThreshold) }})</small>
                                                    @endif
                                                </span>
                                                @if ($shipping == 0)
                                                    <span class="text-success fw-bold">Free</span>
                                                @else
                                                    <span class="fw-bold">₹{{ number_format($shipping) }}</span>
                                                @endif
                                            </div>

                                            <hr>

                                            <div class="summary-row d-flex justify-content-between mb-3">
                                                <span class="h6 mb-0">Total</span>
                                                <span class="h5 text-primary fw-bold mb-0">₹{{ number_format($total) }}</span>
                                            </div>

                                            <button class="btn btn-primary w-100 btn-lg mb-2" onclick="proceedToCheckout()">
                                                <i class="fas fa-lock me-2"></i>Proceed to Checkout
                                            </button>

                                            <a href="{{ route('collections') }}" class="btn btn-outline-secondary w-100">
                                                <i class="fas fa-arrow-left me-2"></i>Continue Shopping
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <!-- Mobile: Sticky Bottom Summary -->
                                <div class="d-lg-none">
                                    <div class="mobile-cart-summary fixed-bottom bg-white border-top shadow-lg p-3">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div>
                                                <small class="text-muted">Total ({{ $cartItems->sum('quantity') }} items)</small>
                                                <div class="h5 text-primary fw-bold mb-0">₹{{ number_format($total) }}</div>
                                            </div>
                                            <button class="btn btn-primary btn-lg px-4" onclick="proceedToCheckout()">
                                                Checkout <i class="fas fa-arrow-right ms-1"></i>
                                            </button>
                                        </div>
                                        <div class="text-center">
                                            <small class="text-muted">
                                                @if ($shipping == 0)
                                                    Free shipping applied
                                                @else
                                                    + ₹{{ number_format($shipping) }} shipping
                                                @endif
                                            </small>
                                        </div>
                                    </div>
                                    <!-- Spacer to prevent content from being hidden behind sticky footer -->
                                    <div style="height: 120px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
                @else
                    <!-- Empty Cart -->
                    <div class="empty-cart-section">
                        <div class="container-fluid px-3 px-md-4">
                            <div class="text-center py-5">
                                <div class="empty-cart-icon mb-4">
                                    <i class="fas fa-shopping-cart text-muted" style="font-size: 4rem;"></i>
                                </div>
                                <h3 class="h4 mb-3">Your cart is empty</h3>
                                <p class="text-muted mb-4">Looks like you haven't added any items to your cart yet.</p>
                                <a href="{{ route('collections') }}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-shopping-bag me-2"></i>Start Shopping
                                </a>
                            </div>
                        </div>
                    </div>
        @endif
    </div>

    <!-- Custom Styles for Modern Cart -->
    <style>
        .modern-cart {
            min-height: 100vh;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .cart-header {
            border-bottom: 1px solid #dee2e6;
            z-index: 1020;
        }

        .cart-content {
            padding-top: 1rem;
            padding-bottom: 2rem;
        }

        .product-image-container img {
            width: 100%;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
        }

        .product-name {
            font-size: 0.95rem;
            line-height: 1.3;
        }

        .product-meta {
            font-size: 0.8rem;
        }

        .price-section .h6 {
            font-size: 1rem;
            color: #e91e63 !important;
        }

        .quantity-controls .btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mobile-cart-summary {
            z-index: 1030;
        }

        .empty-cart-section {
            min-height: 60vh;
            display: flex;
            align-items: center;
        }

        @media (max-width: 991.98px) {
            .cart-content {
                padding-bottom: 140px;
                /* Space for sticky footer */
            }
        }

        /* Card hover effects */
        .cart-item-modern .card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .cart-item-modern .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
        }

        /* Button styles */
        .btn-primary {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #ad1457, #880e4f);
            transform: translateY(-1px);
        }
    </style>

    <!-- JavaScript for Cart Functionality -->
    <script>
        function updateQuantity(itemId, change) {
            const currentQty = parseInt(document.querySelector(`#cart-item-${itemId} .quantity-controls span`).textContent);
            const newQty = Math.max(1, currentQty + change);

            fetch('/cart/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        item_id: itemId,
                        quantity: newQty
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload(); // Reload to update all prices and totals
                    } else {
                        alert('Error updating quantity');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error updating quantity');
                });
        }

        function removeFromCart(itemId) {
            if (confirm('Are you sure you want to remove this item from your cart?')) {
                fetch('/cart/remove', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            item_id: itemId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert('Error removing item');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error removing item');
                    });
            }
        }

        function clearCart() {
            if (confirm('Are you sure you want to clear your entire cart?')) {
                fetch('/cart/clear', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert('Error clearing cart');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error clearing cart');
                    });
            }
        }

        function proceedToCheckout() {
            window.location.href = '/checkout';
        }
    </script>
@endsection
