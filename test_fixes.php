<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "Testing the fixes...\n\n";

// Test 1: Check if models load without errors
try {
    echo "1. Testing Size model...\n";
    $size = new \App\Models\Size();
    echo "   ✓ Size model loads successfully\n";
    
    echo "2. Testing ProductSize model...\n";
    $productSize = new \App\Models\ProductSize();
    echo "   ✓ ProductSize model loads successfully\n";
    
    echo "3. Testing Product model...\n";
    $product = new \App\Models\Product();
    echo "   ✓ Product model loads successfully\n";
    
} catch (Exception $e) {
    echo "   ✗ Error: " . $e->getMessage() . "\n";
}

// Test 2: Check database connections and counts
try {
    echo "\n4. Testing database connections...\n";
    $productCount = \App\Models\Product::count();
    echo "   ✓ Products in database: $productCount\n";
    
    $sizeCount = \App\Models\Size::count();
    echo "   ✓ Sizes in database: $sizeCount\n";
    
    $productSizeCount = \App\Models\ProductSize::count();
    echo "   ✓ Product-Size relationships: $productSizeCount\n";
    
} catch (Exception $e) {
    echo "   ✗ Database error: " . $e->getMessage() . "\n";
}

// Test 3: Check if products have sizes
try {
    echo "\n5. Testing product-size relationships...\n";
    $productsWithSizes = \App\Models\Product::whereHas('productSizes')->count();
    echo "   ✓ Products with sizes: $productsWithSizes\n";
    
    if ($productsWithSizes > 0) {
        $product = \App\Models\Product::with('productSizes.size')->first();
        echo "   ✓ Sample product: " . $product->name . "\n";
        echo "   ✓ Category: " . $product->category->name . "\n";
        
        $sizes = $product->productSizes->map(function($ps) {
            return $ps->size->display_name . " (+" . $ps->price_adjustment . ")";
        })->join(', ');
        echo "   ✓ Available sizes: " . $sizes . "\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ Product-size relationship error: " . $e->getMessage() . "\n";
}

// Test 4: Check accessor methods
try {
    echo "\n6. Testing accessor methods...\n";
    
    $size = \App\Models\Size::first();
    if ($size) {
        $displayName = $size->full_display_name;
        echo "   ✓ Size full_display_name accessor works: " . $displayName . "\n";
    }
    
    $productSize = \App\Models\ProductSize::first();
    if ($productSize) {
        $stockStatus = $productSize->stock_status;
        echo "   ✓ ProductSize stock_status accessor works: " . $stockStatus . "\n";
        
        $stockMessage = $productSize->stock_status_message;
        echo "   ✓ ProductSize stock_status_message accessor works: " . $stockMessage . "\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ Accessor method error: " . $e->getMessage() . "\n";
}

echo "\n✓ All tests completed!\n";
