@extends('layouts.app')

@section('title', 'Kanha Fashion Hub - Exquisite Fashion Collection | Premium Handmade Fashion Jewelry')
@section('description', 'Discover beautiful fashion jewellery at Kanha Fashion Hub. Premium rings, necklaces, earrings, and bracelets with free shipping across India. Shop authentic fashion jewellery.')
@section('keywords', 'fashion jewelry, rings, necklaces, earrings, bracelets, handmade jewelry, fashion accessories, Indian fashion jewelry, wedding jewelry, engagement rings, traditional jewelry, modern jewelry')
@section('og_title', 'Kanha Fashion Hub - Exquisite Fashion Collection')
@section('og_description', 'Discover beautiful fashion jewellery at Kanha Fashion Hub. Premium rings, necklaces, earrings, and bracelets with free shipping across India.')
@section('content')
<!-- Hero Section -->
<section id="home" class="hero-section position-relative overflow-hidden" style="background: linear-gradient(135deg, #FFF5F7 0%, #FFFBF0 100%); padding: 80px 0;">
    <!-- Decorative Elements -->
    <div class="position-absolute top-0 start-0 w-100 h-100" style="opacity: 0.05; pointer-events: none;">
        <div class="position-absolute" style="top: 10%; left: 5%; width: 100px; height: 100px; background: var(--primary-pink); border-radius: 50%; filter: blur(40px);"></div>
        <div class="position-absolute" style="bottom: 20%; right: 10%; width: 150px; height: 150px; background: var(--primary-brown); border-radius: 50%; filter: blur(60px);"></div>
    </div>

    <div class="container position-relative">
        <div class="row align-items-center g-4 my-3">
            <div class="col-lg-6 mt-3" data-aos="fade-right">
                <div class="mb-3">
                    <span class="badge rounded-pill px-3 py-2" style="background: linear-gradient(135deg, var(--primary-pink), var(--primary-brown)); color: white; font-size: 0.85rem;">
                        <i class="fas fa-sparkles me-1"></i> Premium Fashion Jewellery
                    </span>
                </div>
                <h1 class="font-julius fw-bold mb-4" style="font-size: clamp(2.5rem, 5vw, 4rem); line-height: 1.2; background: linear-gradient(135deg, var(--primary-pink), var(--primary-brown)); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                    Kanha Fashion Hub
                </h1>
                <p class="lead mb-4 text-muted font-body" style="font-size: 1.1rem; line-height: 1.8;">
                    At Kanha Fashion Hub, we supply premium fashion jewellery that combines <strong>elegance</strong>, <strong>quality</strong>, and <strong>modern design</strong>. Our collections are crafted with skin-friendly, durable materials — no gold or silver, just pure style and exceptional craftsmanship.
                </p>
                <div class="mb-4 p-4 rounded-3" style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(10px); border: 1px solid rgba(0,0,0,0.05);">
                    <h5 class="font-cursive-bold mb-2" style="color: var(--primary-brown);">
                        <i class="fas fa-star text-warning me-2"></i>Reliable. Stylish. Trusted.
                    </h5>
                    <p class="text-muted font-body mb-0" style="font-style: italic; font-weight: 300;">Your brand, our craftsmanship.</p>
                </div>
                <div class="d-flex flex-wrap gap-3">
                    <a href="{{ url('/collections') }}" class="btn btn-lg font-modern shadow-lg" style="background: linear-gradient(135deg, var(--primary-brown), #8B6F47); color: white; border-radius: 50px; padding: 15px 35px; font-weight: 600; transition: all 0.3s;">
                        <i class="fas fa-gem me-2"></i>Shop Collection
                    </a>
                    <button type="button" class="btn btn-lg btn-outline-dark shadow" style="border-radius: 50px; padding: 15px 35px; font-weight: 600;" data-bs-toggle="modal" data-bs-target="#storyVideoModal">
                        <i class="fas fa-play me-2"></i>Our Story
                    </button>
                </div>

                <!-- Trust Badges -->
                <div class="row mt-5 g-3">
                    <div class="col-4 text-center">
                        <div class="p-3 rounded-3" style="background: rgba(255, 255, 255, 0.5);">
                            <i class="fas fa-shield-alt fs-3 mb-2" style="color: var(--primary-pink);"></i>
                            <h6 class="mb-0 small fw-bold">Quality Assured</h6>
                        </div>
                    </div>
                    <div class="col-4 text-center">
                        <div class="p-3 rounded-3" style="background: rgba(255, 255, 255, 0.5);">
                            <i class="fas fa-shipping-fast fs-3 mb-2" style="color: var(--primary-brown);"></i>
                            <h6 class="mb-0 small fw-bold">Fast Delivery</h6>
                        </div>
                    </div>
                    <div class="col-4 text-center">
                        <div class="p-3 rounded-3" style="background: rgba(255, 255, 255, 0.5);">
                            <i class="fas fa-certificate fs-3 mb-2" style="color: var(--primary-pink);"></i>
                            <h6 class="mb-0 small fw-bold">Certified</h6>
                        </div>
                    </div>
                </div>

                <!-- Stats -->
                {{-- <div class="row mt-5">
                    <div class="col-4">
                        <h3 class="font-playfair text-primary-pink mb-0">500+</h3>
                        <small class="text-muted">Happy Customers</small>
                    </div>
                    <div class="col-4">
                        <h3 class="font-playfair text-primary-pink mb-0">50+</h3>
                        <small class="text-muted">Unique Designs</small>
                    </div>
                    <div class="col-4">
                        <h3 class="font-playfair text-primary-pink mb-0">5★</h3>
                        <small class="text-muted">Customer Rating</small>
                    </div>
                </div> --}}
            </div>

            <div class="col-lg-6" data-aos="fade-left">
                <div class="hero-image-container position-relative">
                    <!-- Main Hero Image -->
                    <div class="hero-image-wrapper position-relative" style="border-radius: 30px; overflow: hidden; box-shadow: 0 20px 60px rgba(0,0,0,0.15);">
                        <div class="position-absolute top-0 start-0 w-100 h-100" style="background: linear-gradient(135deg, rgba(255,182,193,0.2), rgba(139,111,71,0.2)); z-index: 1;"></div>
                        <img src="{{ asset('images/homepagepic.jpg') }}"
                             alt="Kanha Fashion Hub - Exquisite Jewellery Collection"
                             class="img-fluid w-100 hero-main-image"
                             style="object-fit: cover; height: 600px;"
                             onerror="this.src='https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'">

                        <!-- Floating Badge -->
                        <div class="position-absolute bottom-0 start-0 m-4 p-3 rounded-3 shadow-lg" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); z-index: 2;">
                            <div class="d-flex align-items-center gap-2">
                                <i class="fas fa-certificate fs-4" style="color: var(--primary-pink);"></i>
                                <div>
                                    <h6 class="mb-0 fw-bold">Certified Quality</h6>
                                    <small class="text-muted">100% Authentic</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Floating Feature Cards -->
                    {{-- <div class="floating-features d-none d-lg-block">
                        <div class="floating-card floating-card-1">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center p-3">
                                    <i class="fas fa-shipping-fast fs-5 mb-2" style="color: var(--primary-brown);"></i>
                                    <small class="d-block font-modern fw-bold">Free Shipping</small>
                                    <small class="text-muted">Above ₹999</small>
                                </div>
                            </div>
                        </div>

                        <div class="floating-card floating-card-2">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center p-3">
                                    <i class="fas fa-hand-sparkles fs-5 mb-2" style="color: var(--primary-brown);"></i>
                                    <small class="d-block font-modern fw-bold">Handmade</small>
                                    <small class="text-muted">With Love</small>
                                </div>
                            </div>
                        </div>
                    </div> --}}
                </div>

                <!-- Mobile Feature Cards -->
                {{-- <div class="row g-3 mt-3 d-lg-none">
                    <div class="col-6">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center p-3">
                                <i class="fas fa-shipping-fast fs-4 mb-2" style="color: var(--primary-brown);"></i>
                                <h6 class="mb-1 font-modern">Free Shipping</h6>
                                <small class="text-muted">On orders above ₹999</small>
                            </div>
                        </div>
                    </div>

                    <div class="col-6">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body text-center p-3">
                                <i class="fas fa-hand-sparkles fs-4 mb-2" style="color: var(--primary-brown);"></i>
                                <h6 class="mb-1 font-modern">Handmade</h6>
                                <small class="text-muted">Crafted with Care</small>
                            </div>
                        </div>
                    </div>
                </div> --}}
            </div>
        </div>
    </div>
</section>

<!-- Updates & Offers Marquee Section -->
@php
    $announcements = \App\Models\Announcement::active()->ordered()->get();
@endphp

@if($announcements->count() > 0)
<section class="py-5" style="background: linear-gradient(135deg, #FFF5F7 0%, #FFFBF0 100%); overflow: hidden;">
    <div class="container">
        <div class="text-center mb-4">
            <h2 class="font-julius fw-bold mb-2" style="color: var(--primary-brown);">
                <i class="fas fa-bullhorn me-2"></i>Latest Updates & Offers
            </h2>
            <p class="text-muted">Stay updated with our latest news and exclusive offers</p>
        </div>

        <!-- Marquee Container -->
        <div class="marquee-container position-relative">
            <div class="marquee-content d-flex gap-4" id="marqueeContent">
                @foreach($announcements as $announcement)
                <div class="announcement-card flex-shrink-0" style="width: 350px; cursor: pointer;"
                     data-bs-toggle="modal"
                     data-bs-target="#announcementModal{{ $announcement->id }}">
                    <div class="card border-0 shadow-lg h-100" style="border-radius: 20px; overflow: hidden; transition: all 0.3s;">
                        <div class="position-relative">
                            <img src="{{ $announcement->image_url }}"
                                 class="card-img-top"
                                 alt="{{ $announcement->title }}"
                                 style="height: 200px; object-fit: cover;">
                            <div class="position-absolute top-0 end-0 m-3">
                                <span class="badge rounded-pill px-3 py-2"
                                      style="background: {{ $announcement->type === 'offer' ? 'linear-gradient(135deg, #FF6B6B, #FF8E53)' : ($announcement->type === 'update' ? 'linear-gradient(135deg, #4E54C8, #8F94FB)' : 'linear-gradient(135deg, #11998E, #38EF7D)') }}; color: white;">
                                    <i class="fas fa-{{ $announcement->type === 'offer' ? 'tag' : ($announcement->type === 'update' ? 'bell' : 'newspaper') }} me-1"></i>
                                    {{ ucfirst($announcement->type) }}
                                </span>
                            </div>
                        </div>
                        <div class="card-body p-4">
                            <h5 class="card-title font-modern fw-bold mb-2" style="color: var(--primary-brown);">
                                {{ Str::limit($announcement->title, 50) }}
                            </h5>
                            <p class="card-text text-muted small mb-3">
                                {{ Str::limit($announcement->description, 80) }}
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="far fa-clock me-1"></i>
                                    {{ $announcement->created_at->diffForHumans() }}
                                </small>
                                <span class="text-primary-pink fw-bold small">
                                    Read More <i class="fas fa-arrow-right ms-1"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach

                <!-- Duplicate for seamless loop -->
                @foreach($announcements as $announcement)
                <div class="announcement-card flex-shrink-0" style="width: 350px; cursor: pointer;"
                     data-bs-toggle="modal"
                     data-bs-target="#announcementModal{{ $announcement->id }}">
                    <div class="card border-0 shadow-lg h-100" style="border-radius: 20px; overflow: hidden; transition: all 0.3s;">
                        <div class="position-relative">
                            <img src="{{ $announcement->image_url }}"
                                 class="card-img-top"
                                 alt="{{ $announcement->title }}"
                                 style="height: 200px; object-fit: cover;">
                            <div class="position-absolute top-0 end-0 m-3">
                                <span class="badge rounded-pill px-3 py-2"
                                      style="background: {{ $announcement->type === 'offer' ? 'linear-gradient(135deg, #FF6B6B, #FF8E53)' : ($announcement->type === 'update' ? 'linear-gradient(135deg, #4E54C8, #8F94FB)' : 'linear-gradient(135deg, #11998E, #38EF7D)') }}; color: white;">
                                    <i class="fas fa-{{ $announcement->type === 'offer' ? 'tag' : ($announcement->type === 'update' ? 'bell' : 'newspaper') }} me-1"></i>
                                    {{ ucfirst($announcement->type) }}
                                </span>
                            </div>
                        </div>
                        <div class="card-body p-4">
                            <h5 class="card-title font-modern fw-bold mb-2" style="color: var(--primary-brown);">
                                {{ Str::limit($announcement->title, 50) }}
                            </h5>
                            <p class="card-text text-muted small mb-3">
                                {{ Str::limit($announcement->description, 80) }}
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="far fa-clock me-1"></i>
                                    {{ $announcement->created_at->diffForHumans() }}
                                </small>
                                <span class="text-primary-pink fw-bold small">
                                    Read More <i class="fas fa-arrow-right ms-1"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
</section>

<!-- Announcement Modals -->
@foreach($announcements as $announcement)
<div class="modal fade" id="announcementModal{{ $announcement->id }}" tabindex="-1" aria-labelledby="announcementModalLabel{{ $announcement->id }}" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content" style="border-radius: 20px; overflow: hidden; border: none;">
            <div class="modal-header border-0 position-relative p-0">
                <img src="{{ $announcement->image_url }}"
                     class="w-100"
                     alt="{{ $announcement->title }}"
                     style="height: 300px; object-fit: cover;">
                <button type="button" class="btn-close position-absolute top-0 end-0 m-3 bg-white rounded-circle p-2 shadow" data-bs-dismiss="modal" aria-label="Close"></button>
                <div class="position-absolute bottom-0 start-0 m-4">
                    <span class="badge rounded-pill px-3 py-2"
                          style="background: {{ $announcement->type === 'offer' ? 'linear-gradient(135deg, #FF6B6B, #FF8E53)' : ($announcement->type === 'update' ? 'linear-gradient(135deg, #4E54C8, #8F94FB)' : 'linear-gradient(135deg, #11998E, #38EF7D)') }}; color: white; font-size: 0.9rem;">
                        <i class="fas fa-{{ $announcement->type === 'offer' ? 'tag' : ($announcement->type === 'update' ? 'bell' : 'newspaper') }} me-1"></i>
                        {{ ucfirst($announcement->type) }}
                    </span>
                </div>
            </div>
            <div class="modal-body p-4 p-md-5">
                <h3 class="font-julius fw-bold mb-3" style="color: var(--primary-brown);">
                    {{ $announcement->title }}
                </h3>
                <div class="mb-4">
                    <p class="text-muted mb-2" style="line-height: 1.8;">
                        {{ $announcement->description }}
                    </p>
                </div>
                <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                    <div>
                        <small class="text-muted d-block">
                            <i class="far fa-calendar me-1"></i>
                            Created: {{ $announcement->created_at->format('M d, Y') }}
                        </small>
                        <small class="text-muted d-block mt-1">
                            <i class="far fa-clock me-1"></i>
                            Updated: {{ $announcement->updated_at->format('M d, Y h:i A') }}
                        </small>
                    </div>
                    @if($announcement->expires_at)
                    <div class="text-end">
                        <small class="text-danger fw-bold">
                            <i class="fas fa-hourglass-half me-1"></i>
                            Expires: {{ $announcement->expires_at->format('M d, Y') }}
                        </small>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endforeach
@endif

<!-- Featured Collections -->
<section id="collections" class="py-5">
    <div class="container">
        <h2 class="section-title">Featured Collections</h2>

        <div class="row g-3 g-md-4">
            @if(isset($featuredCategories) && $featuredCategories->count() > 0)
                @foreach($featuredCategories as $category)
                    <div class="col-12 col-sm-6 col-lg-3">
                        <div class="card product-card h-100 border-0 shadow-sm">
                            <div class="position-relative overflow-hidden">
                                <img src="{{ $category->image_url }}"
                                     class="card-img-top" alt="{{ $category->name }} Collection"
                                     style="height: 250px; object-fit: cover;"
                                     onerror="this.src='https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'">
                                <div class="product-overlay">
                                    <a href="{{ route('collections.category', $category->slug) }}" class="btn btn-light rounded-pill">
                                        <i class="fas fa-eye me-2"></i>View Collection
                                    </a>
                                </div>
                            </div>
                            <div class="card-body text-center">
                                <h5 class="card-title font-playfair">{{ $category->name }}</h5>
                                <p class="card-text text-muted">{{ $category->description }}</p>
                                <div class="d-flex justify-content-center align-items-center">
                                    @php
                                        $minPrice = $category->products()->active()->inStock()->min('price');
                                        $productCount = $category->products()->active()->inStock()->count();
                                    @endphp
                                    @if($minPrice)
                                        <span class="fw-bold font-modern" style="color: var(--primary-brown);">From ₹{{ number_format($minPrice) }}</span>
                                    @endif
                                    @if($productCount > 0)
                                        <small class="text-muted ms-2">({{ $productCount }} items)</small>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <!-- Fallback static collections if no featured categories found -->
                <div class="col-12 col-sm-6 col-lg-3">
                    <div class="card product-card h-100 border-0 shadow-sm">
                        <div class="position-relative overflow-hidden">
                            <img src="https://images.unsplash.com/photo-1605100804763-247f67b3557e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                                 class="card-img-top" alt="Rings Collection" style="height: 250px; object-fit: cover;">
                            <div class="product-overlay">
                                <a href="{{ route('collections') }}" class="btn btn-light rounded-pill">
                                    <i class="fas fa-eye me-2"></i>View Collection
                                </a>
                            </div>
                        </div>
                        <div class="card-body text-center">
                            <h5 class="card-title font-playfair">Elegant Rings</h5>
                            <p class="card-text text-muted">Discover our stunning ring collection</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <div class="text-center mt-5">
            <a href="{{ route('collections') }}" class="btn btn-outline-pink btn-lg">
                <i class="fas fa-th me-2"></i>View All Collections
            </a>
        </div>
    </div>
</section>

<!-- Featured Products -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="section-title">Featured Products</h2>
        <p class="text-center text-muted mb-5">Handpicked pieces from our exclusive collection</p>

        @if(isset($featuredProducts) && $featuredProducts->count() > 0)
            <div class="row g-3 g-md-4">
                @foreach($featuredProducts->take(4) as $product)
                    <div class="col-12 col-sm-6 col-lg-3">
                        <div class="card product-card h-100 border-0 shadow-sm">
                            <div class="position-relative overflow-hidden">
                                @if($product->images && count($product->images) > 0)
                                    @php
                                        $imagePath = $product->images[0];
                                        // Check if it's a full URL
                                        if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
                                            $imageUrl = $imagePath;
                                        } else {
                                            // Clean the path and ensure storage/ prefix
                                            $cleanPath = str_replace(['\\/', '\\'], '/', $imagePath);
                                            // Add storage/ prefix if not already present
                                            if (!str_starts_with($cleanPath, 'storage/')) {
                                                $cleanPath = 'storage/' . ltrim($cleanPath, '/');
                                            }
                                            $imageUrl = asset($cleanPath);
                                        }
                                    @endphp
                                    <img src="{{ $imageUrl }}"
                                         alt="{{ $product->name }}"
                                         class="card-img-top"
                                         style="height: 250px; object-fit: cover;"
                                         onerror="this.src='https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'">
                                @else
                                    <img src="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                                         alt="{{ $product->name }}"
                                         class="card-img-top"
                                         style="height: 250px; object-fit: cover;">
                                @endif

                                <!-- Product badges -->
                                <div class="position-absolute top-0 start-0 p-2">
                                    <span class="badge" style="background-color: var(--primary-brown); color: var(--primary-cream);">Featured</span>
                                    @if($product->isOnSale())
                                        <span class="badge bg-warning ms-1">Sale</span>
                                    @endif
                                </div>

                                <!-- Removed overlay for cleaner design -->
                            </div>

                            <div class="card-body text-center">
                                <div class="mb-2">
                                    <small class="text-muted">{{ $product->category->name }}</small>
                                </div>
                                <h5 class="card-title font-playfair mb-2">{{ $product->name }}</h5>
                                <p class="card-text text-muted small mb-3">{{ Str::limit($product->short_description, 60) }}</p>

                                <div class="d-flex justify-content-center align-items-center mb-3">
                                    @if($product->isOnSale())
                                        <span class="fw-bold me-2 font-modern" style="color: var(--primary-brown);">₹{{ number_format($product->sale_price) }}</span>
                                        <span class="text-muted text-decoration-line-through small">₹{{ number_format($product->price) }}</span>
                                    @else
                                        <span class="fw-bold font-modern" style="color: var(--primary-brown);">₹{{ number_format($product->price) }}</span>
                                    @endif
                                </div>

                                <!-- Price Display -->
                                <div class="mb-3">
                                    @if($product->isOnSale())
                                        <div class="d-flex align-items-center justify-content-center gap-2">
                                            <span class="h5 mb-0 text-success fw-bold">₹{{ number_format($product->sale_price) }}</span>
                                            <small class="text-muted text-decoration-line-through">₹{{ number_format($product->price) }}</small>
                                        </div>
                                    @else
                                        <div class="text-center">
                                            <span class="h5 mb-0 fw-bold">₹{{ number_format($product->price) }}</span>
                                        </div>
                                    @endif
                                </div>

                                <!-- Add to Cart Button -->
                                @if($product->in_stock)
                                    <button class="btn btn-primary-pink w-100 mb-2 add-to-cart-btn"
                                        data-product-id="{{ $product->id }}" style="font-weight: 600; padding: 12px;">
                                        <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                                    </button>
                                    <div class="d-flex gap-2 mb-2">
                                        <a href="{{ route('product.detail', $product->slug) }}"
                                           class="btn btn-outline-primary btn-sm flex-fill">
                                            <i class="fas fa-eye me-1"></i>View Details
                                        </a>
                                        <button class="btn btn-outline-secondary btn-sm flex-fill wishlist-btn"
                                            data-product-id="{{ $product->id }}">
                                            <i class="fas fa-heart me-1"></i>Wishlist
                                        </button>
                                    </div>
                                    <span class="badge bg-success-subtle text-success">In Stock</span>
                                @else
                                    <button class="btn btn-secondary w-100 mb-2" disabled>
                                        <i class="fas fa-times me-2"></i>Out of Stock
                                    </button>
                                    <a href="{{ route('product.detail', $product->slug) }}"
                                       class="btn btn-outline-primary btn-sm w-100 mb-2">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </a>
                                    <span class="badge bg-danger-subtle text-danger">Out of Stock</span>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="text-center mt-5">
                <a href="{{ route('collections') }}?sort=featured" class="btn btn-lg font-modern" style="background-color: var(--primary-brown); color: var(--primary-cream); border-radius: 25px; padding: 15px 30px; font-weight: 600;">
                    <i class="fas fa-star me-2"></i>View All Featured Products
                </a>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-gem text-muted mb-3" style="font-size: 3rem;"></i>
                <h4 class="text-muted">No Featured Products Available</h4>
                <p class="text-muted">Check back soon for our handpicked collection!</p>
                <a href="{{ route('collections') }}" class="btn btn-outline-pink">
                    <i class="fas fa-th me-2"></i>Browse All Products
                </a>
            </div>
        @endif
    </div>
</section>

<!-- Testimonials Section -->
{{-- <section class="py-5 bg-light">
    <div class="container">
        <h2 class="section-title">What Our Customers Say</h2>

        <div class="row g-4">
            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text mb-4">"Absolutely stunning jewelry! The quality is exceptional and the designs are so unique. I've received countless compliments on my necklace."</p>
                        <div class="d-flex align-items-center justify-content-center">
                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                                 alt="Customer" class="rounded-circle me-3" width="50" height="50">
                            <div>
                                <h6 class="mb-0">Priya Sharma</h6>
                                <small class="text-muted">Mumbai</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text mb-4">"ShreeJi has the most beautiful collection I've ever seen. The craftsmanship is incredible and the customer service is outstanding."</p>
                        <div class="d-flex align-items-center justify-content-center">
                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                                 alt="Customer" class="rounded-circle me-3" width="50" height="50">
                            <div>
                                <h6 class="mb-0">Anita Patel</h6>
                                <small class="text-muted">Delhi</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text mb-4">"Perfect for my wedding! The team helped me choose the most beautiful set. Every piece is a work of art. Highly recommended!"</p>
                        <div class="d-flex align-items-center justify-content-center">
                            <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                                 alt="Customer" class="rounded-circle me-3" width="50" height="50">
                            <div>
                                <h6 class="mb-0">Kavya Reddy</h6>
                                <small class="text-muted">Bangalore</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section> --}}

<!-- Our Story Video Modal -->
<div class="modal fade" id="storyVideoModal" tabindex="-1" aria-labelledby="storyVideoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header border-0 pb-0">
                <h5 class="modal-title font-playfair" id="storyVideoModalLabel" style="color: var(--primary-brown);">
                    <i class="fas fa-heart me-2"></i>Our Story
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div class="ratio ratio-16x9">
                    <video id="storyVideo" class="rounded-bottom" controls preload="metadata" poster="{{ asset('images/homepagepic.jpg') }}">
                        <source src="{{ asset('video/kanhastory.mp4') }}" type="video/mp4">
                        <p class="text-center p-4">
                            Your browser doesn't support HTML5 video.
                            <a href="{{ asset('video/kanhastory.mp4') }}" download>Download the video</a> instead.
                        </p>
                    </video>
                </div>
            </div>
            <div class="modal-footer border-0 pt-2">
                <div class="text-center w-100">
                    <p class="text-muted mb-2 font-body">
                        <i class="fas fa-sparkles me-1" style="color: var(--primary-brown);"></i>
                        Discover the passion behind every piece
                    </p>
                    <a href="{{ url('/about') }}" class="btn btn-sm font-modern" style="background-color: var(--primary-brown); color: var(--primary-cream); border-radius: 20px; padding: 8px 20px;">
                        <i class="fas fa-arrow-right me-1"></i>Learn More About Us
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Mobile-First Welcome Page Styles */

    /* Enhanced Typography for Mobile */
    @media (max-width: 576px) {
        .hero-section {
            padding: 2rem 0;
        }

        .hero-section .container {
            padding: 0 1rem;
        }

        .brand-text {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .font-julius {
            font-size: 1.4rem;
            line-height: 1.2;
            display: block;
            margin-top: 0.5rem;
        }

        .tagline-text {
            text-align: center;
            font-size: 0.95rem;
        }

        .lead {
            font-size: 0.9rem;
            line-height: 1.5;
            text-align: center;
        }

        /* .btn {
            width: 100%;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            padding: 12px 20px;
        } */

        .card-body {
            padding: 1rem 0.75rem;
        }

        .card-title {
            font-size: 1rem;
            line-height: 1.3;
        }

        .card-text {
            font-size: 0.85rem;
            line-height: 1.4;
        }
    }

    /* Tablet Styles */
    @media (min-width: 577px) and (max-width: 768px) {
        .font-julius {
            font-size: 1.8rem;
            line-height: 1.2;
        }

        .tagline-text {
            font-size: 1.1rem;
        }

        .lead {
            font-size: 1rem;
            line-height: 1.6;
        }
    }

    /* Desktop Styles */
    @media (min-width: 769px) {
        .font-julius {
            font-size: 2.2rem;
            line-height: 1.1;
        }

        .tagline-text {
            font-size: 1.3rem;
        }

        .lead {
            font-size: 1.1rem;
            line-height: 1.7;
        }
    }

    /* Enhanced Button Styles */
    .btn {
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        border: none;
        box-shadow: 0 4px 15px rgba(139, 69, 19, 0.2);
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
    }

    .btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 10px rgba(139, 69, 19, 0.2);
    }

    /* Enhanced Card Styles */
    .card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }

    /* Typography Enhancements */
    .font-julius {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .font-cursive-bold {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .font-modern {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .font-body {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* Accessibility Improvements */
    @media (prefers-reduced-motion: reduce) {
        .btn, .card {
            transition: none;
        }

        .btn:hover, .card:hover {
            transform: none;
        }
    }

    /* High DPI Display Optimizations */
    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        .font-julius, .font-cursive-bold, .font-modern, .font-body {
            -webkit-font-smoothing: subpixel-antialiased;
        }
    }

    /* Hero Image Enhancements */
    .hero-image-container {
        position: relative;
        z-index: 1;
    }

    .hero-image-wrapper {
        position: relative;
        overflow: hidden;
        border-radius: 1.5rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .hero-main-image {
        width: 100%;
        height: auto;
        min-height: 300px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .hero-main-image:hover {
        transform: scale(1.02);
    }

    /* Floating Cards */
    .floating-features {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 2;
    }

    .floating-card {
        position: absolute;
        pointer-events: auto;
        animation: float 6s ease-in-out infinite;
    }

    .floating-card-1 {
        top: 15%;
        left: -10%;
        animation-delay: 0s;
    }

    .floating-card-2 {
        bottom: 20%;
        right: -10%;
        animation-delay: 3s;
    }

    .floating-card .card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transform: scale(0.9);
        transition: all 0.3s ease;
    }

    .floating-card .card:hover {
        transform: scale(1);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    /* Video Modal Enhancements */
    .modal-content {
        border-radius: 1rem;
        overflow: hidden;
    }

    .modal-header {
        background: linear-gradient(135deg, rgba(139, 69, 19, 0.05), rgba(233, 30, 99, 0.05));
    }

    .modal-footer {
        background: linear-gradient(135deg, rgba(139, 69, 19, 0.02), rgba(233, 30, 99, 0.02));
    }

    #storyVideo {
        border-radius: 0 0 1rem 1rem;
    }

    /* Mobile Optimizations */
    @media (max-width: 991px) {
        .hero-main-image {
            min-height: 250px;
        }

        .floating-features {
            display: none !important;
        }
    }

    /* Marquee Styles */
    .marquee-container {
        overflow: hidden;
        position: relative;
        width: 100%;
    }

    .marquee-content {
        display: flex;
        animation: marquee 40s linear infinite;
        will-change: transform;
    }

    .marquee-content:hover {
        animation-play-state: paused;
    }

    @keyframes marquee {
        0% {
            transform: translateX(0);
        }
        100% {
            transform: translateX(-50%);
        }
    }

    .announcement-card .card {
        transition: all 0.3s ease;
    }

    .announcement-card .card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2) !important;
    }

    @media (max-width: 576px) {
        .hero-main-image {
            min-height: 200px;
        }

        .modal-dialog {
            margin: 1rem;
        }
    }
</style>
@endpush

@push('structured-data')
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Kanha Fashion Hub",
    "alternateName": "Kanha Fashion Hub",
    "url": "{{ url('/') }}",
    "logo": "{{ asset('images/logo.png') }}",
    "description": "Kanha Fashion Hub - Exquisite fashion jewellery collection. Premium rings, necklaces, earrings, and bracelets with free shipping across India.",
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "IN"
    },
    "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "availableLanguage": ["English", "Hindi"]
    },
    "sameAs": [
        "https://www.facebook.com/kanhafashionhub",
        "https://www.instagram.com/kanhafashionhub",
        "https://www.twitter.com/kanhafashionhub"
    ]
}
</script>

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Kanha Fashion Hub",
    "url": "{{ url('/') }}",
    "description": "Discover beautiful fashion jewellery at Kanha Fashion Hub. Premium rings, necklaces, earrings, and bracelets with free shipping across India.",
    "potentialAction": {
        "@type": "SearchAction",
        "target": {
            "@type": "EntryPoint",
            "urlTemplate": "{{ url('/collections') }}?search={search_term_string}"
        },
        "query-input": "required name=search_term_string"
    }
}
</script>

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Store",
    "name": "Kanha Fashion Hub",
    "image": "{{ asset('images/store-front.jpg') }}",
    "description": "Premium fashion jewellery store offering exquisite rings, necklaces, earrings, and bracelets.",
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "IN"
    },
    "geo": {
        "@type": "GeoCoordinates",
        "latitude": "19.0760",
        "longitude": "72.8777"
    },
    "url": "{{ url('/') }}",
    "telephone": "+91-XXXXXXXXXX",
    "priceRange": "₹₹₹",
    "paymentAccepted": ["Cash", "Credit Card", "Debit Card", "UPI", "Net Banking"],
    "currenciesAccepted": "INR",
    "openingHours": "Mo-Sa 10:00-20:00"
}
</script>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const storyVideoModal = document.getElementById('storyVideoModal');
    const storyVideo = document.getElementById('storyVideo');

    if (storyVideoModal && storyVideo) {
        // Pause video when modal is closed
        storyVideoModal.addEventListener('hidden.bs.modal', function () {
            storyVideo.pause();
            storyVideo.currentTime = 0;
        });

        // Optional: Auto-play when modal opens (be careful with browser policies)
        storyVideoModal.addEventListener('shown.bs.modal', function () {
            // Uncomment the next line if you want auto-play (may not work on all browsers/devices)
            // storyVideo.play().catch(e => console.log('Auto-play prevented:', e));
        });
    }
});
</script>
@endpush
