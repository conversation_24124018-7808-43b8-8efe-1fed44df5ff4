<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::with('category')->active()->inStock();

        // Filter by category
        if ($request->has('category') && $request->category) {
            $query->byCategory($request->category);
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhere('sku', 'like', "%{$searchTerm}%");
            });
        }

        // Filter by price range
        if ($request->has('min_price') && $request->min_price) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->has('max_price') && $request->max_price) {
            $query->where('price', '<=', $request->max_price);
        }

        // Filter by metal type
        if ($request->has('metal_type') && $request->metal_type) {
            $query->where('metal_type', $request->metal_type);
        }

        // Sort functionality
        $sortBy = $request->get('sort', 'name');
        $sortOrder = $request->get('order', 'asc');

        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'featured':
                $query->orderBy('is_featured', 'desc')->orderBy('name', 'asc');
                break;
            default:
                $query->orderBy($sortBy, $sortOrder);
        }

        $products = $query->paginate(12);
        $categories = Category::active()->ordered()->get();

        return view('collections', compact('products', 'categories'));
    }

    public function show($slug)
    {
        $product = Product::with(['category', 'productSizes.size', 'approvedReviews.user'])
            ->where('slug', $slug)
            ->active()
            ->firstOrFail();

        // Get available sizes with stock information
        $availableSizes = $product->getAvailableSizesWithStock();

        // Get related products with intelligent matching
        $relatedProducts = $this->getRelatedProducts($product);

        // Get reviews with pagination
        $reviews = $product->approvedReviews()
            ->with('user')
            ->latest()
            ->paginate(5);

        return view('product.detail', compact('product', 'availableSizes', 'relatedProducts', 'reviews'));
    }

    private function getRelatedProducts($product)
    {
        // Priority 1: Same category products
        $sameCategoryProducts = Product::with('category')
            ->where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->active()
            ->inStock()
            ->inRandomOrder()
            ->limit(2)
            ->get();

        // Priority 2: Same metal type (if available)
        $sameMetalProducts = collect();
        if ($product->metal_type) {
            $sameMetalProducts = Product::with('category')
                ->where('metal_type', $product->metal_type)
                ->where('id', '!=', $product->id)
                ->whereNotIn('id', $sameCategoryProducts->pluck('id'))
                ->active()
                ->inStock()
                ->inRandomOrder()
                ->limit(1)
                ->get();
        }

        // Priority 3: Similar price range
        $priceMin = $product->price * 0.7; // 30% lower
        $priceMax = $product->price * 1.3; // 30% higher
        $similarPriceProducts = Product::with('category')
            ->whereBetween('price', [$priceMin, $priceMax])
            ->where('id', '!=', $product->id)
            ->whereNotIn('id', $sameCategoryProducts->pluck('id'))
            ->whereNotIn('id', $sameMetalProducts->pluck('id'))
            ->active()
            ->inStock()
            ->inRandomOrder()
            ->limit(1)
            ->get();

        // Combine all related products
        $relatedProducts = $sameCategoryProducts
            ->merge($sameMetalProducts)
            ->merge($similarPriceProducts);

        // If we still don't have 4 products, fill with random products
        if ($relatedProducts->count() < 4) {
            $additionalProducts = Product::with('category')
                ->where('id', '!=', $product->id)
                ->whereNotIn('id', $relatedProducts->pluck('id'))
                ->active()
                ->inStock()
                ->inRandomOrder()
                ->limit(4 - $relatedProducts->count())
                ->get();

            $relatedProducts = $relatedProducts->merge($additionalProducts);
        }

        return $relatedProducts->take(4);
    }

    public function category($categorySlug)
    {
        $category = Category::with(['children', 'parent'])
            ->where('slug', $categorySlug)
            ->active()
            ->firstOrFail();

        // Get products from this category and all its subcategories
        $categoryIds = $category->getDescendants()->pluck('id')->push($category->id);

        $products = Product::with(['category', 'productSizes.size'])
            ->whereIn('category_id', $categoryIds)
            ->active()
            ->inStock()
            ->paginate(12);

        // Get category hierarchy for navigation
        $categories = Category::with('children')->roots()->active()->ordered()->get();

        // Get breadcrumb for current category
        $breadcrumb = $category->getBreadcrumb();

        return view('collections', compact('products', 'category', 'categories', 'breadcrumb'));
    }

    public function search(Request $request)
    {
        $searchTerm = $request->get('q', '');
        $products = collect();
        $categories = Category::active()->ordered()->get();

        if (strlen($searchTerm) >= 2) {
            $products = Product::with('category')
                ->where(function($query) use ($searchTerm) {
                    $query->where('name', 'like', "%{$searchTerm}%")
                          ->orWhere('description', 'like', "%{$searchTerm}%")
                          ->orWhere('sku', 'like', "%{$searchTerm}%")
                          ->orWhere('metal_type', 'like', "%{$searchTerm}%")
                          ->orWhere('stone_type', 'like', "%{$searchTerm}%");
                })
                ->active()
                ->inStock()
                ->paginate(12);
        }

        return view('collections', compact('products', 'searchTerm', 'categories'));
    }

    public function featured()
    {
        $products = Product::with('category')->featured()->active()->inStock()->limit(8)->get();
        return response()->json($products);
    }

    /**
     * Get available sizes for a product with stock information
     */
    public function getProductSizes($id)
    {
        try {
            $product = Product::with(['productSizes.size', 'category'])->findOrFail($id);

            $sizes = $product->productSizes->map(function ($productSize) {
                return [
                    'id' => $productSize->size->id,
                    'name' => $productSize->size->name,
                    'display_name' => $productSize->size->full_display_name,
                    'stock_quantity' => $productSize->stock_quantity,
                    'stock_status' => $productSize->stock_status,
                    'stock_status_message' => $productSize->stock_status_message,
                    'price_adjustment' => $productSize->price_adjustment,
                    'final_price' => $productSize->final_price,
                    'is_available' => $productSize->is_available && $productSize->isInStock(),
                    'sku' => $productSize->full_sku,
                ];
            });

            return response()->json([
                'success' => true,
                'product_id' => $product->id,
                'product_name' => $product->name,
                'base_price' => $product->price,
                'sale_price' => $product->sale_price,
                'sizes' => $sizes
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load product sizes: ' . $e->getMessage()
            ], 500);
        }
    }
}
