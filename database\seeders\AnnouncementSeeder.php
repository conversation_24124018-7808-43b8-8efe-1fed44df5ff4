<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Announcement;
use Carbon\Carbon;

class AnnouncementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $announcements = [
            [
                'title' => '🎉 Grand Diwali Sale - Up to 50% OFF!',
                'description' => 'Celebrate this Diwali with amazing discounts on all ethnic wear. Shop now and get up to 50% off on sarees, lehengas, and kurtas. Limited time offer!',
                'image' => 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                'type' => 'offer',
                'is_active' => true,
                'sort_order' => 1,
                'starts_at' => Carbon::now()->subDays(2),
                'expires_at' => Carbon::now()->addDays(15),
            ],
            [
                'title' => '✨ New Collection Launch - Winter Festive 2024',
                'description' => 'Introducing our exclusive Winter Festive Collection! Explore the latest designs in traditional and contemporary wear. Perfect for weddings and celebrations.',
                'image' => 'https://images.unsplash.com/photo-1610030469983-98e550d6193c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                'type' => 'update',
                'is_active' => true,
                'sort_order' => 2,
                'starts_at' => Carbon::now()->subDay(),
                'expires_at' => Carbon::now()->addDays(30),
            ],
            [
                'title' => '🎁 Free Shipping on Orders Above ₹999',
                'description' => 'Great news! Get free shipping on all orders above ₹999. Shop your favorite ethnic wear and enjoy doorstep delivery at no extra cost.',
                'image' => 'https://images.unsplash.com/photo-1607082349566-187342175e2f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                'type' => 'news',
                'is_active' => true,
                'sort_order' => 3,
                'starts_at' => Carbon::now()->subDays(5),
                'expires_at' => null, // No expiry
            ],
            [
                'title' => '💝 Special Bridal Collection Now Available',
                'description' => 'Make your special day even more memorable with our exclusive bridal collection. Featuring designer lehengas, sarees, and accessories.',
                'image' => 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                'type' => 'update',
                'is_active' => true,
                'sort_order' => 4,
                'starts_at' => null, // Started immediately
                'expires_at' => Carbon::now()->addDays(60),
            ],
            [
                'title' => '🌟 Flat 30% OFF on Designer Sarees',
                'description' => 'Exclusive offer on designer sarees! Get flat 30% discount on our premium collection. Use code: SAREE30 at checkout.',
                'image' => 'https://images.unsplash.com/photo-1610030469983-98e550d6193c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                'type' => 'offer',
                'is_active' => true,
                'sort_order' => 5,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addDays(10),
            ],
            [
                'title' => '📢 Store Timings Updated for Festival Season',
                'description' => 'We are now open extended hours during the festival season! Visit us from 9 AM to 9 PM daily. Happy shopping!',
                'image' => 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                'type' => 'news',
                'is_active' => true,
                'sort_order' => 6,
                'starts_at' => Carbon::now()->subDays(3),
                'expires_at' => Carbon::now()->addDays(20),
            ],
        ];

        foreach ($announcements as $announcement) {
            Announcement::create($announcement);
        }

        $this->command->info('Announcements seeded successfully!');
    }
}

