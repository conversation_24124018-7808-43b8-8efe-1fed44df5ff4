@extends('layouts.admin')

@section('title', 'Admin Dashboard - Kanha Fashion Hub')

@section('content')
<!-- <PERSON> Header -->
<div class="row align-items-center mb-4">
    <div class="col-12 col-md-6">
        <h1 class="h3 mb-1 text-gray-800 font-playfair">Admin Dashboard</h1>
        <p class="mb-0 text-muted d-none d-md-block">Manage your Kanha Fashion Hub store</p>
    </div>
    <div class="col-12 col-md-6 mt-3 mt-md-0">
        <div class="row g-3">
            <div class="col-6">
                <div class="text-center p-3 bg-light rounded">
                    <div class="h5 mb-0 text-success">₹{{ number_format($todayRevenue / 100000, 2) }}L</div>
                    <small class="text-muted">Today's Sales</small>
                </div>
            </div>
            <div class="col-6">
                <div class="text-center p-3 bg-light rounded">
                    <div class="h5 mb-0 text-primary">{{ $todayOrders }}</div>
                    <small class="text-muted">New Orders</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row g-3 g-md-4 mb-4 mb-md-5">
    <div class="col-6 col-lg-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-3 p-md-4">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle d-flex align-items-center justify-content-center me-2 me-md-3"
                         style="width: 45px; height: 45px; background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink)); color: white;">
                        <i class="fas fa-shopping-bag fs-6 fs-md-4"></i>
                    </div>
                    <div class="flex-fill">
                        <h4 class="font-playfair mb-0 fs-5 fs-md-3" style="color: var(--primary-pink);">{{ $totalOrders }}</h4>
                        <p class="text-muted mb-0 small">Total Orders</p>
                        <small class="text-success d-none d-md-inline">{{ $pendingOrders }} pending</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-3 p-md-4">
                <div class="d-flex align-items-center">
                    <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-2 me-md-3"
                         style="width: 45px; height: 45px;">
                        <i class="fas fa-rupee-sign fs-6 fs-md-4"></i>
                    </div>
                    <div class="flex-fill">
                        <h4 class="font-playfair text-success mb-0 fs-5 fs-md-3">₹{{ number_format($todayRevenue / 100000, 1) }}L</h4>
                        <p class="text-muted mb-0 small">Today's Revenue</p>
                        <small class="text-success d-none d-md-inline">{{ $todayOrders }} orders today</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-3 p-md-4">
                <div class="d-flex align-items-center">
                    <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-2 me-md-3"
                         style="width: 45px; height: 45px;">
                        <i class="fas fa-users fs-6 fs-md-4"></i>
                    </div>
                    <div class="flex-fill">
                        <h4 class="font-playfair text-info mb-0 fs-5 fs-md-3">{{ number_format($totalCustomers) }}</h4>
                        <p class="text-muted mb-0 small">Customers</p>
                        <small class="text-success d-none d-md-inline">{{ $newCustomers }} new today</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-3 p-md-4">
                <div class="d-flex align-items-center">
                    <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-2 me-md-3"
                         style="width: 45px; height: 45px;">
                        <i class="fas fa-gem fs-6 fs-md-4"></i>
                    </div>
                    <div class="flex-fill">
                        <h4 class="font-playfair text-warning mb-0 fs-5 fs-md-3">{{ $totalProducts }}</h4>
                        <p class="text-muted mb-0 small">Products</p>
                        <small class="text-info d-none d-md-inline">{{ $activeProducts }} active</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row g-3 g-md-4">
    <!-- Recent Orders -->
    <div class="col-12 col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center gap-2">
                <h5 class="mb-0">Recent Orders</h5>
                <a href="{{ route('admin.orders.tracking.index') }}" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-eye me-1 d-none d-sm-inline"></i>View All
                </a>
            </div>
            <div class="card-body p-0">
                <!-- Mobile Order Cards -->
                <div class="d-md-none">
                    @forelse($recentOrders ?? [] as $order)
                    <div class="border-bottom p-3">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div>
                                <h6 class="mb-1">{{ $order->order_number ?? 'ORD-2024-001' }}</h6>
                                <small class="text-muted">{{ $order->user->name ?? 'John Doe' }}</small>
                            </div>
                            <span class="badge bg-primary">{{ $order->getStatusDisplayName() ?? 'Pending' }}</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <small class="text-muted">{{ $order->orderItems->count() ?? 1 }} item(s)</small>
                                <div class="fw-bold text-success">₹{{ number_format($order->total_amount ?? 15000, 0) }}</div>
                            </div>
                            <div class="d-flex gap-1">
                                <a href="{{ route('admin.orders.tracking.show', $order->id ?? 1) }}"
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="btn btn-outline-success btn-sm"
                                        onclick="quickUpdateStatus({{ $order->id ?? 1 }}, '{{ $order->status ?? 'pending' }}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No recent orders found</p>
                    </div>
                    @endforelse
                </div>

                <!-- Desktop Table -->
                <div class="table-responsive d-none d-md-block">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Items</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($recentOrders ?? [] as $order)
                            <tr>
                                <td><strong>{{ $order->order_number ?? 'ORD-2024-001' }}</strong></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded-circle d-flex align-items-center justify-content-center me-2"
                                             style="width: 30px; height: 30px; font-size: 12px; background-color: var(--primary-brown); color: var(--primary-cream);">
                                            {{ substr($order->user->name ?? 'User', 0, 1) }}
                                        </div>
                                        <span>{{ $order->user->name ?? 'Unknown User' }}</span>
                                    </div>
                                </td>
                                <td>{{ $order->orderItems->count() ?? 1 }} item(s)</td>
                                <td><strong>₹{{ number_format($order->total_amount ?? 15000, 0) }}</strong></td>
                                <td>
                                    <span class="badge bg-primary">
                                        {{ $order->getStatusDisplayName() ?? 'Pending' }}
                                    </span>
                                </td>
                                <td>{{ $order->created_at->format('M d, Y') ?? date('M d, Y') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.orders.tracking.show', $order->id ?? 1) }}"
                                           class="btn btn-outline-primary btn-sm" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-outline-success btn-sm"
                                                onclick="quickUpdateStatus({{ $order->id ?? 1 }}, '{{ $order->status ?? 'pending' }}')"
                                                title="Update Status">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">No recent orders found</p>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-12 col-lg-4">
        <div class="card border-0 shadow-sm mb-3 mb-md-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <!-- Mobile: 2 columns, Desktop: 1 column -->
                <div class="row g-2 d-md-none">
                    <div class="col-6">
                        <a href="{{ route('admin.products.create') }}" class="btn w-100 btn-sm" style="background-color: var(--primary-brown); color: var(--primary-cream);">
                            <i class="fas fa-plus d-block mb-1"></i>
                            <small>Add Product</small>
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ route('admin.orders.tracking.index') }}" class="btn btn-outline-primary w-100 btn-sm">
                            <i class="fas fa-truck d-block mb-1"></i>
                            <small>Tracking</small>
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ route('admin.orders.index') }}" class="btn btn-outline-secondary w-100 btn-sm">
                            <i class="fas fa-list d-block mb-1"></i>
                            <small>Orders</small>
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ route('admin.customers.index') }}" class="btn btn-outline-info w-100 btn-sm">
                            <i class="fas fa-users d-block mb-1"></i>
                            <small>Customers</small>
                        </a>
                    </div>
                </div>

                <!-- Desktop: Full width buttons -->
                <div class="d-none d-md-grid gap-2">
                    <a href="{{ route('admin.products.create') }}" class="btn" style="background-color: var(--primary-brown); color: var(--primary-cream);">
                        <i class="fas fa-plus me-2"></i>Add New Product
                    </a>
                    <a href="{{ route('admin.orders.tracking.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-truck me-2"></i>Order Tracking
                    </a>
                    <a href="{{ route('admin.orders.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i>Manage Orders
                    </a>
                    <a href="{{ route('admin.customers.index') }}" class="btn btn-outline-info">
                        <i class="fas fa-users me-2"></i>View Customers
                    </a>
                    <button class="btn btn-outline-success">
                        <i class="fas fa-chart-bar me-2"></i>View Reports
                    </button>
                </div>
            </div>
        </div>
                
                <!-- Top Products -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Top Selling Products</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <img src="https://images.unsplash.com/photo-1605100804763-247f67b3557e?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&q=80" 
                                 alt="Product" class="rounded me-3" width="50" height="50">
                            <div class="flex-fill">
                                <h6 class="mb-0">Diamond Solitaire Ring</h6>
                                <small class="text-muted">47 sold this month</small>
                            </div>
                            <span class="badge bg-success">₹45K</span>
                        </div>
                        
                        <div class="d-flex align-items-center mb-3">
                            <img src="https://images.unsplash.com/photo-1535632066927-ab7c9ab60908?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&q=80" 
                                 alt="Product" class="rounded me-3" width="50" height="50">
                            <div class="flex-fill">
                                <h6 class="mb-0">Pearl Drop Earrings</h6>
                                <small class="text-muted">32 sold this month</small>
                            </div>
                            <span class="badge bg-success">₹12K</span>
                        </div>
                        
                        <div class="d-flex align-items-center">
                            <img src="https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&q=80" 
                                 alt="Product" class="rounded me-3" width="50" height="50">
                            <div class="flex-fill">
                                <h6 class="mb-0">Gold Chain Necklace</h6>
                                <small class="text-muted">28 sold this month</small>
                            </div>
                            <span class="badge bg-success">₹32K</span>
                        </div>
                    </div>
                </div>

                <!-- Pages Management -->
                <div class="card border-0 shadow-sm mt-3 mt-md-4">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Pages Management</h5>
                        <a href="{{ route('admin.pages.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                    <div class="card-body">
                        @php
                            $totalPages = \App\Models\Page::count();
                            $publishedPages = \App\Models\Page::where('is_published', true)->count();
                            $draftPages = \App\Models\Page::where('is_published', false)->count();
                            $recentPages = \App\Models\Page::latest()->take(3)->get();
                        @endphp

                        <!-- Page Stats -->
                        <div class="row g-2 mb-3">
                            <div class="col-4">
                                <div class="text-center p-2 bg-light rounded">
                                    <div class="h6 mb-0 text-primary">{{ $totalPages }}</div>
                                    <small class="text-muted">Total</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center p-2 bg-light rounded">
                                    <div class="h6 mb-0 text-success">{{ $publishedPages }}</div>
                                    <small class="text-muted">Published</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center p-2 bg-light rounded">
                                    <div class="h6 mb-0 text-warning">{{ $draftPages }}</div>
                                    <small class="text-muted">Drafts</small>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Pages -->
                        @if($recentPages->count() > 0)
                        <h6 class="mb-2">Recent Pages</h6>
                        @foreach($recentPages as $page)
                        <div class="d-flex align-items-center mb-2">
                            <div class="flex-fill">
                                <h6 class="mb-0 small">{{ Str::limit($page->title, 25) }}</h6>
                                <small class="text-muted">{{ $page->updated_at->diffForHumans() }}</small>
                            </div>
                            <div class="d-flex gap-1">
                                @if($page->is_published)
                                <span class="badge bg-success">Published</span>
                                @else
                                <span class="badge bg-warning">Draft</span>
                                @endif
                                <a href="{{ route('admin.pages.edit', $page) }}"
                                   class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                        </div>
                        @endforeach
                        @else
                        <div class="text-center py-3">
                            <i class="fas fa-file-alt fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-2">No pages yet</p>
                            <a href="{{ route('admin.pages.create') }}" class="btn btn-sm" style="background-color: var(--primary-brown); color: var(--primary-cream);">
                                <i class="fas fa-plus me-1"></i>Create First Page
                            </a>
                        </div>
                        @endif

                        <!-- Quick Actions -->
                        <div class="d-grid gap-2 mt-3">
                            <a href="{{ route('admin.pages.create') }}" class="btn btn-sm" style="background-color: var(--primary-brown); color: var(--primary-cream);">
                                <i class="fas fa-plus me-2"></i>Create New Page
                            </a>
                            <a href="{{ route('admin.pages.index') }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-list me-2"></i>Manage All Pages
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Charts Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row g-4">
            <!-- Sales Chart -->
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">Sales Overview</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-placeholder bg-light rounded d-flex align-items-center justify-content-center" style="height: 300px;">
                            <div class="text-center">
                                <i class="fas fa-chart-line fs-1 mb-3" style="color: var(--primary-brown);"></i>
                                <h5 class="text-muted">Sales Chart</h5>
                                <p class="text-muted">Chart integration would go here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Category Performance -->
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">Category Performance</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-1">
                                <span>Rings</span>
                                <span>45%</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar" style="width: 45%; background-color: var(--primary-brown);"></div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-1">
                                <span>Necklaces</span>
                                <span>30%</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-success" style="width: 30%"></div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-1">
                                <span>Earrings</span>
                                <span>20%</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-info" style="width: 20%"></div>
                            </div>
                        </div>
                        
                        <div class="mb-0">
                            <div class="d-flex justify-content-between mb-1">
                                <span>Bracelets</span>
                                <span>5%</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-warning" style="width: 5%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recent Activity -->
<section class="py-5">
    <div class="container">
        <div class="row g-4">
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Recent Activity</h5>
                    </div>
                    <div class="card-body">
                        <div class="activity-item d-flex align-items-start mb-3">
                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                                 style="width: 40px; height: 40px; min-width: 40px;">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">New order received</h6>
                                <p class="text-muted mb-1">Order #ORD-2024-047 from Priya Sharma</p>
                                <small class="text-muted">2 minutes ago</small>
                            </div>
                        </div>
                        
                        <div class="activity-item d-flex align-items-start mb-3">
                            <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                                 style="width: 40px; height: 40px; min-width: 40px;">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">New customer registered</h6>
                                <p class="text-muted mb-1">Rajesh Kumar joined as VIP member</p>
                                <small class="text-muted">15 minutes ago</small>
                            </div>
                        </div>
                        
                        <div class="activity-item d-flex align-items-start">
                            <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                                 style="width: 40px; height: 40px; min-width: 40px;">
                                <i class="fas fa-star"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">New product review</h6>
                                <p class="text-muted mb-1">5-star review for Diamond Solitaire Ring</p>
                                <small class="text-muted">1 hour ago</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Low Stock Alerts</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning d-flex align-items-center mb-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <div>
                                <strong>Diamond Earrings</strong> - Only 2 units left
                            </div>
                        </div>
                        
                        <div class="alert alert-warning d-flex align-items-center mb-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <div>
                                <strong>Gold Bracelet</strong> - Only 1 unit left
                            </div>
                        </div>
                        
                        <div class="alert alert-info d-flex align-items-center mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            <div>
                                <strong>Silver Ring Set</strong> - 5 units remaining
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
    /* Admin Dashboard Mobile-First Styles */

    /* Mobile Stats Cards */
    @media (max-width: 575.98px) {
        .fs-5 {
            font-size: 1rem !important;
        }

        .fs-6 {
            font-size: 0.9rem !important;
        }

        .small {
            font-size: 0.8rem !important;
        }
    }

    /* Mobile Order Cards */
    .mobile-order-card {
        transition: all 0.3s ease;
    }

    .mobile-order-card:hover {
        background-color: #f8f9fa;
    }

    /* Mobile Quick Actions */
    @media (max-width: 767.98px) {
        .btn-sm {
            padding: 0.5rem 0.25rem;
            font-size: 0.8rem;
            text-align: center;
        }

        .btn-sm i {
            font-size: 1.2rem;
        }

        .btn-sm small {
            font-size: 0.7rem;
            line-height: 1;
        }
    }

    /* Mobile Table Improvements */
    .table-responsive {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    @media (max-width: 767.98px) {
        .table-responsive {
            font-size: 0.85rem;
        }

        .btn-group .btn {
            padding: 0.25rem 0.5rem;
        }
    }

    /* Mobile Charts */
    @media (max-width: 767.98px) {
        .chart-placeholder {
            height: 200px !important;
        }

        .chart-placeholder .fs-1 {
            font-size: 2rem !important;
        }

        .chart-placeholder h5 {
            font-size: 1rem !important;
        }

        .chart-placeholder p {
            font-size: 0.8rem !important;
        }
    }

    /* Mobile Progress Bars */
    @media (max-width: 767.98px) {
        .progress {
            height: 6px !important;
        }
    }

    /* Mobile Activity Items */
    @media (max-width: 767.98px) {
        .activity-item .bg-success,
        .activity-item .bg-info,
        .activity-item .bg-warning {
            width: 35px !important;
            height: 35px !important;
            min-width: 35px !important;
            font-size: 0.9rem;
        }

        .activity-item h6 {
            font-size: 0.9rem;
        }

        .activity-item p {
            font-size: 0.8rem;
        }

        .activity-item small {
            font-size: 0.7rem;
        }
    }

    /* Mobile Alerts */
    @media (max-width: 767.98px) {
        .alert {
            padding: 0.5rem 0.75rem;
            font-size: 0.85rem;
        }

        .alert i {
            font-size: 0.9rem;
        }
    }

    /* Mobile Top Products */
    @media (max-width: 767.98px) {
        .top-products img {
            width: 40px !important;
            height: 40px !important;
        }

        .top-products h6 {
            font-size: 0.9rem;
        }

        .top-products small {
            font-size: 0.75rem;
        }

        .top-products .badge {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
        }
    }

    /* Mobile Card Headers */
    @media (max-width: 767.98px) {
        .card-header h5 {
            font-size: 1rem;
        }

        .card-header .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }
    }

    /* Mobile Badge Improvements */
    @media (max-width: 767.98px) {
        .badge {
            font-size: 0.7rem;
            padding: 0.3rem 0.6rem;
        }
    }

    /* Touch-friendly improvements */
    @media (max-width: 767.98px) {
        .btn {
            min-height: 44px;
        }

        .btn-sm {
            min-height: 36px;
        }

        .card {
            margin-bottom: 1rem;
        }
    }

    /* Loading states */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    /* Notification positioning for mobile */
    @media (max-width: 767.98px) {
        .alert.position-fixed {
            top: 70px !important;
            left: 10px !important;
            right: 10px !important;
            width: auto !important;
            min-width: auto !important;
        }
    }
</style>
@endpush

@push('scripts')
<script>
function quickUpdateStatus(orderId, currentStatus) {
    // Define possible next statuses based on current status
    const statusTransitions = {
        'pending': ['confirmed', 'cancelled'],
        'confirmed': ['processing', 'cancelled'],
        'processing': ['packed', 'cancelled'],
        'packed': ['shipped'],
        'shipped': ['out_for_delivery', 'delivered'],
        'out_for_delivery': ['delivered'],
        'delivered': [],
        'cancelled': [],
        'refunded': []
    };

    const statusNames = {
        'confirmed': 'Confirmed',
        'processing': 'Processing',
        'packed': 'Packed',
        'shipped': 'Shipped',
        'out_for_delivery': 'Out for Delivery',
        'delivered': 'Delivered',
        'cancelled': 'Cancelled'
    };

    const possibleStatuses = statusTransitions[currentStatus] || [];

    if (possibleStatuses.length === 0) {
        showAlert('No status updates available for this order.', 'info');
        return;
    }

    // Create options for the prompt
    let options = possibleStatuses.map((status, index) =>
        `${index + 1}. ${statusNames[status]}`
    ).join('\n');

    const choice = prompt(`Select new status for Order #${orderId}:\n\n${options}\n\nEnter number (1-${possibleStatuses.length}):`);

    if (choice && choice >= 1 && choice <= possibleStatuses.length) {
        const newStatus = possibleStatuses[choice - 1];
        const notes = prompt('Add notes (optional):');

        updateOrderStatus(orderId, newStatus, notes);
    }
}

function updateOrderStatus(orderId, status, notes) {
    fetch(`/admin/orders/tracking/${orderId}/status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            status: status,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            // Refresh the page to show updated status
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error updating order status', 'danger');
    });
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Auto-refresh dashboard every 30 seconds
setInterval(() => {
    // Only refresh if user is still on the page
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 30000);
</script>
@endpush

@push('styles')
<style>
    .progress-bar {
        background-color: var(--primary-brown) !important;
    }
    
    .chart-placeholder {
        border: 2px dashed #dee2e6;
    }
    
    .activity-item:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }
</style>
@endpush
