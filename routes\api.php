<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Public API routes
Route::get('/products/featured', [App\Http\Controllers\ProductController::class, 'featured']);
Route::get('/cart/count', [App\Http\Controllers\CartController::class, 'count']);

// Location API routes (public)
Route::get('/location/countries', [App\Http\Controllers\LocationController::class, 'getCountries']);
Route::get('/location/states', [App\Http\Controllers\LocationController::class, 'getStates']);

// Test route removed - use /api/location/pincode/{pincode} for production

// Authenticated API routes
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/cart/add', [App\Http\Controllers\CartController::class, 'add']);
    Route::put('/cart/{id}', [App\Http\Controllers\CartController::class, 'update']);
    Route::delete('/cart/{id}', [App\Http\Controllers\CartController::class, 'remove']);
    Route::delete('/cart', [App\Http\Controllers\CartController::class, 'clear']);

    Route::put('/profile', [App\Http\Controllers\UserController::class, 'updateProfile']);
    Route::put('/profile/password', [App\Http\Controllers\UserController::class, 'updatePassword']);
    Route::put('/profile/preferences', [App\Http\Controllers\UserController::class, 'updatePreferences']);
    Route::delete('/profile', [App\Http\Controllers\UserController::class, 'deleteAccount']);

    Route::post('/orders', [App\Http\Controllers\OrderController::class, 'store']);
    Route::put('/orders/{id}/cancel', [App\Http\Controllers\OrderController::class, 'cancel']);
});
