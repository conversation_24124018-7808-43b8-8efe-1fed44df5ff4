<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class CustomerController extends Controller
{
    /**
     * Display a listing of customers
     */
    public function index(Request $request)
    {
        $query = User::where('role', 'customer')
            ->withCount('orders')
            ->with('orders');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        $customers = $query->latest()->paginate(20);

        return view('admin.customers.index', compact('customers'));
    }

    /**
     * Display the specified customer
     */
    public function show($id)
    {
        $customer = User::where('role', 'customer')
            ->with(['orders' => function($query) {
                $query->latest();
            }])
            ->findOrFail($id);

        return view('admin.customers.show', compact('customer'));
    }

    /**
     * Update customer status
     */
    public function updateStatus(Request $request, $id)
    {
        $customer = User::where('role', 'customer')->findOrFail($id);

        $request->validate([
            'status' => 'required|in:active,inactive,blocked'
        ]);

        $customer->update([
            'status' => $request->status
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Customer status updated successfully'
        ]);
    }

    /**
     * Update customer membership
     */
    public function updateMembership(Request $request, $id)
    {
        $customer = User::where('role', 'customer')->findOrFail($id);

        $request->validate([
            'membership_tier' => 'required|in:bronze,silver,gold,platinum'
        ]);

        $customer->update([
            'membership_tier' => $request->membership_tier
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Customer membership updated successfully'
        ]);
    }

    /**
     * Export customers
     */
    public function export(Request $request)
    {
        $customers = User::where('role', 'customer')
            ->withCount('orders')
            ->get();

        $filename = 'customers_' . date('Y-m-d_His') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($customers) {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($file, ['ID', 'Name', 'Email', 'Phone', 'Total Orders', 'Total Spent', 'Status', 'Created At']);

            // Add customer data
            foreach ($customers as $customer) {
                fputcsv($file, [
                    $customer->id,
                    $customer->name,
                    $customer->email,
                    $customer->phone,
                    $customer->orders_count,
                    $customer->total_spent ?? 0,
                    $customer->status ?? 'active',
                    $customer->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}

