<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="@yield('description', 'Kanha Fashion Hub - Exquisite Fashion Collection. Discover beautiful rings, necklaces, earrings and more. Premium quality fashion jewellery with free shipping.')">
        <meta name="keywords" content="@yield('keywords', 'fashion jewelry, rings, necklaces, earrings, bracelets, fashion jewellery, fashion accessories, wedding jewelry, engagement rings, Indian jewelry, traditional jewelry, modern jewelry')">
        <meta name="author" content="Kanha Fashion Hub">
        <meta name="robots" content="@yield('robots', 'index, follow')">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <!-- Canonical URL -->
        <link rel="canonical" href="@yield('canonical', request()->url())">

        <!-- Open Graph Meta Tags -->
        <meta property="og:title" content="@yield('og_title', 'Kanha Fashion Hub - Exquisite Fashion Collection')">
        <meta property="og:description" content="@yield('og_description', 'Kanha Fashion Hub - Exquisite Fashion Collection. Discover beautiful rings, necklaces, earrings and more. Premium quality handmade fashion jewelry with free shipping.')">
        <meta property="og:image" content="@yield('og_image', asset('images/og-image.jpg'))">
        <meta property="og:url" content="@yield('og_url', request()->url())">
        <meta property="og:type" content="@yield('og_type', 'website')">
        <meta property="og:site_name" content="Kanha Fashion Hub">
        <meta property="og:locale" content="en_IN">

        <!-- Twitter Card Meta Tags -->
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="@yield('twitter_title', 'Kanha Fashion Hub - Exquisite Fashion Collection')">
        <meta name="twitter:description" content="@yield('twitter_description', 'Kanha Fashion Hub - Exquisite Fashion Collection. Discover beautiful rings, necklaces, earrings and more.')">
        <meta name="twitter:image" content="@yield('twitter_image', asset('images/twitter-card.jpg'))">

        <!-- Additional SEO Meta Tags -->
        <meta name="theme-color" content="#E91E63">
        <meta name="msapplication-TileColor" content="#E91E63">
        <meta name="application-name" content="Kanha Fashion Hub">

        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
        <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('apple-touch-icon.png') }}">
        <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('favicon-32x32.png') }}">
        <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('favicon-16x16.png') }}">
        <link rel="manifest" href="{{ asset('site.webmanifest') }}">

        <title>@yield('title', 'Kanha Fashion Hub - Exquisite Fashion Collection | Premium Handmade Fashion Jewelry')</title>

        <!-- Structured Data (JSON-LD) -->
        @stack('structured-data')

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Julius+Sans+One&family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        
        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        
        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

        <!-- Custom Styles -->
        <style>
            :root {
                /* ===== PROFESSIONAL PINK-GOLD COLOR SYSTEM ===== */

                /* Primary Brand Colors */
                --primary-pink: #E91E63;
                --secondary-pink: #F06292;
                --dark-pink: #C2185B;
                --light-pink: #F8BBD9;
                --accent-pink: #FF4081;

                /* Gold Accent Colors */
                --primary-gold: #FFD700;
                --secondary-gold: #FFC107;
                --dark-gold: #FF8F00;
                --light-gold: #FFF9C4;
                --rose-gold: #E8B4B8;
                --champagne-gold: #F7E7CE;

                /* Neutral Colors */
                --white: #FFFFFF;
                --light-gray: #F8F9FA;
                --medium-gray: #E9ECEF;
                --dark-gray: #6C757D;
                --text-dark: #2C3E50;
                --text-light: #6C757D;
                --border-light: #DEE2E6;

                /* Semantic Colors */
                --success: #28A745;
                --warning: #FFC107;
                --danger: #DC3545;
                --info: #17A2B8;

                /* Layout Variables */
                --navbar-height: 80px;
                --border-radius: 12px;
                --border-radius-lg: 20px;
                --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                --box-shadow-lg: 0 8px 30px rgba(0, 0, 0, 0.15);
                --transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

                /* Legacy Compatibility (will be phased out) */
                --primary-brown: #E91E63;
                --secondary-brown: #F06292;
                --dark-brown: #C2185B;
                --light-brown: #F8BBD9;
                --primary-cream: #FFF9C4;
                --secondary-cream: #F7E7CE;
                --warm-cream: #FFC1CC;
                --accent-cream: #FFD700;
                --blush-pink: #FFC1CC;
                --dusty-rose: #DCAE96;
            }

            body {
                font-family: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
                color: var(--text-dark);
                line-height: 1.6;
                padding-top: var(--navbar-height);
                margin: 0;
                min-height: 100vh;
                display: flex;
                flex-direction: column;
                overflow-x: hidden;
                width: 100%;
                font-size: 16px;
                font-weight: 400;
            }

            /* Ensure no gaps between navbar and content */
            .main-content > section:first-child,
            .main-content > .hero-section {
                margin-top: 0 !important;
                padding-top: 0 !important;
            }

            /* Global layout fixes */
            html, body {
                overflow-x: hidden;
                width: 100%;
                max-width: 100%;
            }

            .container, .container-fluid {
                max-width: 100%;
                overflow-x: hidden;
            }

            .row {
                margin-left: 0;
                margin-right: 0;
            }

            /* Mobile layout adjustments */
            @media (max-width: 991.98px) {
                body {
                    overflow-x: hidden;
                }
            }

            /* Safe area support for devices with notches */
            @media (max-width: 991.98px) {
                body {
                    padding-bottom: constant(safe-area-inset-bottom);
                    padding-bottom: env(safe-area-inset-bottom);
                }
            }

            /* ===== PROFESSIONAL TYPOGRAPHY SYSTEM ===== */

            /* Font Families */
            .font-julius {
                font-family: 'Julius Sans One', sans-serif;
                font-weight: 400;
                letter-spacing: 0.5px;
                text-rendering: optimizeLegibility;
            }

            .font-cursive-bold {
                font-family: 'Poppins', sans-serif;
                font-weight: 700;
                font-style: italic;
                letter-spacing: 0.3px;
                text-rendering: optimizeLegibility;
            }

            .font-playfair {
                font-family: 'Poppins', 'Inter', sans-serif;
                font-weight: 600;
                text-rendering: optimizeLegibility;
                -webkit-font-smoothing: antialiased;
            }

            .font-modern {
                font-family: 'Poppins', 'Inter', sans-serif;
                font-weight: 500;
                text-rendering: optimizeLegibility;
            }

            .font-body {
                font-family: 'Inter', 'Roboto', sans-serif;
                font-weight: 400;
                line-height: 1.6;
                text-rendering: optimizeLegibility;
            }

            /* Typography Hierarchy */
    
            .lead {
                font-size: 1.125rem;
                font-weight: 400;
                color: var(--text-light);
                line-height: 1.6;
            }

            .text-muted {
                color: var(--text-light) !important;
            }

            /* Brand Typography */
            .brand-text {
                font-family: 'Julius Sans One', sans-serif;
                font-weight: 400;
                letter-spacing: 1px;
                color: var(--primary-pink);
            }

            .tagline-text {
                font-family: 'Inter', sans-serif;
                font-weight: 300;
                letter-spacing: 0.5px;
                color: var(--text-light);
            }

            /* Mobile-First Typography Sizes */
            @media (max-width: 576px) {
                .brand-text {
                    font-size: 1.5rem !important;
                    line-height: 1.3;
                }

                .tagline-text {
                    font-size: 1rem !important;
                    line-height: 1.4;
                }

                h1 {
                    font-size: 2rem !important;
                    line-height: 1.2;
                }

                h2 {
                    font-size: 1.5rem !important;
                    line-height: 1.3;
                }

                h3 {
                    font-size: 1.25rem !important;
                    line-height: 1.3;
                }

                h4 {
                    font-size: 1.1rem !important;
                    line-height: 1.3;
                }

                h5 {
                    font-size: 1rem !important;
                    line-height: 1.3;
                }

                p, .lead {
                    font-size: 0.95rem !important;
                    line-height: 1.5;
                }
            }

            @media (min-width: 577px) and (max-width: 768px) {
                .brand-text {
                    font-size: 1.75rem !important;
                    line-height: 1.3;
                }

                .tagline-text {
                    font-size: 1.1rem !important;
                    line-height: 1.4;
                }
            }

            @media (min-width: 769px) {
                .brand-text {
                    font-size: 2.25rem !important;
                    line-height: 1.2;
                }

                .tagline-text {
                    font-size: 1.25rem !important;
                    line-height: 1.3;
                }
            }

            .text-primary-pink {
                color: var(--primary-pink) !important;
            }

            .text-primary-gold {
                color: var(--primary-gold) !important;
            }

            .bg-primary-pink {
                background-color: var(--primary-pink) !important;
            }

            .bg-primary-gold {
                background-color: var(--primary-gold) !important;
            }

            .bg-gradient-pink {
                background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink));
            }

            .bg-gradient-gold {
                background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
            }

            .bg-gradient-pink-gold {
                background: linear-gradient(135deg, var(--primary-pink), var(--primary-gold));
            }

            /* ===== PROFESSIONAL BUTTON SYSTEM ===== */

            /* Base Button Improvements */
            .btn {
                border-radius: var(--border-radius);
                font-weight: 600;
                padding: 12px 24px;
                transition: var(--transition);
                border: none;
                text-transform: none;
                letter-spacing: 0.5px;
                font-size: 0.95rem;
                line-height: 1.5;
                position: relative;
                overflow: hidden;
                cursor: pointer;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
                min-height: 44px;
            }

            /* Primary Pink Button */
            .btn-primary-pink {
                background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink));
                border: none;
                color: white;
                font-weight: 600;
                padding: 12px 30px;
                border-radius: var(--border-radius);
                transition: var(--transition);
                box-shadow: var(--box-shadow);
            }

            .btn-primary-pink:hover {
                background: linear-gradient(135deg, var(--dark-pink), var(--primary-pink));
                color: white;
                transform: translateY(-2px);
                box-shadow: var(--box-shadow-lg);
            }

            .btn-primary-pink:focus {
                box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.25);
                outline: none;
            }

            /* Outline Pink Button */
            .btn-outline-pink {
                border: 2px solid var(--primary-pink);
                color: var(--primary-pink);
                background: transparent;
                font-weight: 600;
                padding: 12px 30px;
                border-radius: var(--border-radius);
                transition: var(--transition);
            }

            .btn-outline-pink:hover {
                background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink));
                color: white;
                border-color: var(--primary-pink);
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(233, 30, 99, 0.3);
            }

            .btn-outline-pink:focus {
                box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.25);
                outline: none;
            }

            /* Gold Button */
            .btn-gold {
                background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
                color: var(--text-dark);
                border: none;
                font-weight: 600;
                padding: 12px 30px;
                border-radius: var(--border-radius);
                transition: var(--transition);
                box-shadow: var(--box-shadow);
            }

            .btn-gold:hover {
                background: linear-gradient(135deg, var(--dark-gold), var(--primary-gold));
                transform: translateY(-2px);
                box-shadow: var(--box-shadow-lg);
                color: var(--text-dark);
            }

            /* Button States */
            .btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none !important;
                box-shadow: none !important;
            }

            .btn:active {
                transform: translateY(0) !important;
            }

            /* Navbar brand styling moved to navbar.blade.php to avoid conflicts */

            /* Main Content */
            .main-content {
                flex: 1;
                min-height: calc(100vh - var(--navbar-height));
                margin: 0;
                padding: 0;
            }

            /* ===== PROFESSIONAL CARD SYSTEM ===== */

            /* Base Card Styles */
            .card {
                border: none;
                border-radius: var(--border-radius-lg);
                overflow: hidden;
                transition: var(--transition);
                box-shadow: var(--box-shadow);
                background: var(--white);
                position: relative;
            }

            .card:hover {
                transform: translateY(-8px);
                box-shadow: var(--box-shadow-lg);
            }

            .card-body {
                padding: 1.5rem;
            }

            .card-header {
                background: transparent;
                border-bottom: 1px solid var(--border-light);
                padding: 1.25rem 1.5rem;
                font-weight: 600;
                color: var(--text-dark);
            }

            .card-footer {
                background: transparent;
                border-top: 1px solid var(--border-light);
                padding: 1.25rem 1.5rem;
            }

            /* Product Card Specific */
            .product-card {
                position: relative;
                overflow: hidden;
                height: 100%;
                display: flex;
                flex-direction: column;
            }

            .product-card .card-img-top {
                transition: var(--transition);
                height: 250px;
                object-fit: cover;
                width: 100%;
            }

            .product-card:hover .card-img-top {
                transform: scale(1.05);
            }

            .product-card .card-body {
                flex: 1;
                display: flex;
                flex-direction: column;
                padding: 1.25rem;
            }

            .product-card .card-title {
                font-size: 1.1rem;
                font-weight: 600;
                color: var(--text-dark);
                margin-bottom: 0.5rem;
                line-height: 1.3;
            }

            .product-card .card-text {
                color: var(--text-light);
                font-size: 0.9rem;
                margin-bottom: 1rem;
                flex: 1;
            }

            /* Card Variants */
            .card-elevated {
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            }

            .card-elevated:hover {
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
                transform: translateY(-12px);
            }

            .card-minimal {
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                border: 1px solid var(--border-light);
            }

            .card-minimal:hover {
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
                transform: translateY(-2px);
            }

            /* ===== PROFESSIONAL FORM SYSTEM ===== */

            /* Form Controls */
            .form-control {
                border: 2px solid var(--border-light);
                border-radius: var(--border-radius);
                padding: 12px 16px;
                font-size: 0.95rem;
                transition: var(--transition);
                background: var(--white);
                color: var(--text-dark);
                min-height: 44px;
            }

            .form-control:focus {
                border-color: var(--primary-pink);
                box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
                outline: none;
                background: var(--white);
            }

            .form-control::placeholder {
                color: var(--text-light);
                opacity: 0.7;
            }

            /* Form Labels */
            .form-label {
                font-weight: 600;
                color: var(--text-dark);
                margin-bottom: 0.5rem;
                font-size: 0.9rem;
            }

            /* Form Select */
            .form-select {
                border: 2px solid var(--border-light);
                border-radius: var(--border-radius);
                padding: 12px 16px;
                font-size: 0.95rem;
                transition: var(--transition);
                background: var(--white);
                color: var(--text-dark);
                min-height: 44px;
            }

            .form-select:focus {
                border-color: var(--primary-pink);
                box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
                outline: none;
            }

            /* Validation States */
            .form-control.is-valid {
                border-color: var(--success);
            }

            .form-control.is-invalid {
                border-color: var(--danger);
            }

            .valid-feedback {
                color: var(--success);
                font-size: 0.875rem;
                font-weight: 500;
            }

            .invalid-feedback {
                color: var(--danger);
                font-size: 0.875rem;
                font-weight: 500;
            }

            /* Section Styles */
            .section-title {
                font-family: 'Playfair Display', serif;
                font-size: 3rem;
                font-weight: 600;
                color: var(--text-dark);
                text-align: center;
                margin-bottom: 3rem;
                position: relative;
            }

            .section-title::after {
                content: '';
                position: absolute;
                width: 80px;
                height: 3px;
                background: linear-gradient(90deg, var(--primary-pink), var(--primary-gold));
                bottom: -10px;
                left: 50%;
                transform: translateX(-50%);
            }

            /* Hero Section */
            .hero-section {
                background: linear-gradient(135deg, rgba(233, 30, 99, 0.08), rgba(255, 215, 0, 0.08));
                min-height: calc(100vh - var(--navbar-height));
                display: flex;
                align-items: center;
                padding: 0 0 2rem 0;
                position: relative;
                overflow: hidden;
                margin-top: 0;
            }

            .hero-section::before {
                content: '';
                position: absolute;
                top: -50%;
                right: -50%;
                width: 100%;
                height: 200%;
                background: radial-gradient(circle, rgba(233, 30, 99, 0.05) 0%, transparent 70%);
                animation: rotate 20s linear infinite;
                pointer-events: none;
            }

            @keyframes rotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            /* Footer */
            .footer {
                background: linear-gradient(135deg, var(--primary-pink), var(--dark-pink));
                color: var(--light-gold);
                padding: 60px 0 30px;
                margin-top: auto;
            }

            .footer h5 {
                color: var(--primary-gold);
                font-family: 'Playfair Display', serif;
                margin-bottom: 20px;
                font-size: 1.1rem;
            }

            .footer a {
                color: var(--champagne-gold);
                text-decoration: none;
                transition: all 0.3s ease;
                display: inline-block;
            }

            .footer a:hover {
                color: var(--primary-gold);
                transform: translateX(3px);
            }

            /* Newsletter Section */
            .newsletter-section {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 20px;
                padding: 2rem;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 215, 0, 0.2);
            }

            .newsletter-form .input-group {
                max-width: 400px;
                margin: 0 auto;
            }

            .newsletter-form .form-control {
                border-radius: 50px 0 0 50px;
                border: none;
                padding: 12px 20px;
                background: rgba(255, 255, 255, 0.95);
            }

            .newsletter-form .btn {
                border-radius: 0 50px 50px 0;
                padding: 12px 25px;
                border: none;
                background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
                color: var(--text-dark);
            }

            /* Footer Links */
            .footer-links li {
                margin-bottom: 8px;
            }

            .footer-links a {
                font-size: 0.9rem;
                padding: 2px 0;
            }

            .footer-contact li {
                margin-bottom: 10px;
                font-size: 0.9rem;
                display: flex;
                align-items: flex-start;
            }

            .footer-contact i {
                margin-top: 2px;
                color: var(--primary-gold);
                flex-shrink: 0;
            }

            /* Social Icons */
            .social-icons {
                display: flex;
                gap: 10px;
                flex-wrap: wrap;
            }

            .social-icons a {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, var(--secondary-pink), var(--primary-pink));
                color: white;
                border-radius: 50%;
                transition: all 0.3s ease;
                font-size: 1rem;
                box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            }

            .social-icons a:hover {
                background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
                transform: translateY(-3px);
                color: var(--text-dark);
                box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
            }

            /* Footer Bottom */
            .footer-bottom {
                margin-top: 2rem;
            }

            .footer-divider {
                border-color: rgba(255, 255, 255, 0.2);
                margin: 2rem 0 1.5rem;
            }

            .footer-legal-links {
                display: flex;
                gap: 1rem;
                flex-wrap: wrap;
                justify-content: center;
            }

            .footer-legal-links a {
                font-size: 0.85rem;
                padding: 0.25rem 0;
            }

            /* Mobile First Responsive Design */

            /* Extra Small devices (phones, 576px and down) */
            @media (max-width: 575.98px) {
                :root {
                    --navbar-height: 80px;
                }

                body {
                    padding-top: 80px;
                }

                .section-title {
                    font-size: 1.75rem;
                    margin-bottom: 2rem;
                }

                .hero-section {
                    min-height: calc(100vh - var(--navbar-height));
                    padding: 0 0 1rem 0;
                    margin-top: 0;
                }

                .hero-section h1 {
                    font-size: 2rem !important;
                    line-height: 1.2;
                    margin-bottom: 1rem !important;
                }

                .hero-section .lead {
                    font-size: 1rem;
                    margin-bottom: 1.5rem !important;
                }

                .btn-primary-pink,
                .btn-outline-pink {
                    padding: 10px 20px;
                    font-size: 0.9rem;
                }

                .card-body {
                    padding: 1rem;
                }

                .product-card .card-img-top {
                    height: 200px;
                }

                /* Hide floating cards on mobile */
                .position-absolute.top-0,
                .position-absolute.bottom-0 {
                    display: none;
                }

                /* Stack stats vertically on very small screens */
                .hero-section .row.mt-5 .col-4 {
                    text-align: center;
                    margin-bottom: 1rem;
                }

                .hero-section .row.mt-5 .col-4 h3 {
                    font-size: 1.5rem;
                }

                /* Footer Mobile Styles */
                .footer {
                    padding: 40px 0 20px;
                }

                .newsletter-section {
                    padding: 1.5rem 1rem;
                    margin-bottom: 2rem;
                }

                .newsletter-section h4 {
                    font-size: 1.25rem;
                }

                .newsletter-form .input-group {
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .newsletter-form .form-control,
                .newsletter-form .btn {
                    border-radius: 50px;
                    width: 100%;
                }

                .footer h5 {
                    font-size: 1rem;
                    margin-bottom: 15px;
                }

                .footer-links a,
                .footer-contact li {
                    font-size: 0.85rem;
                }

                .footer-contact li {
                    margin-bottom: 8px;
                }

                .social-icons {
                    justify-content: center;
                    margin-top: 1rem;
                }

                .footer-legal-links {
                    justify-content: center;
                    gap: 0.75rem;
                }

                .footer-legal-links a {
                    font-size: 0.8rem;
                }

                .footer-bottom {
                    text-align: center;
                }

                .footer-divider {
                    margin: 1.5rem 0 1rem;
                }
            }

            /* Small devices (landscape phones, 576px and up) */
            @media (min-width: 576px) and (max-width: 767.98px) {
                :root {
                    --navbar-height: 80px;
                }

                body {
                    padding-top: 80px;
                }

                .section-title {
                    font-size: 2rem;
                    margin-bottom: 2.5rem;
                }

                .hero-section h1 {
                    font-size: 2.5rem !important;
                }

                .product-card .card-img-top {
                    height: 220px;
                }

                /* Footer Small Device Styles */
                .newsletter-form .input-group {
                    flex-direction: row;
                }

                .newsletter-form .form-control {
                    border-radius: 50px 0 0 50px;
                }

                .newsletter-form .btn {
                    border-radius: 0 50px 50px 0;
                }

                .footer-legal-links {
                    justify-content: center;
                }
            }

            /* Medium devices (tablets, 768px and up) */
            @media (min-width: 768px) and (max-width: 991.98px) {
                :root {
                    --navbar-height: 80px;
                }

                body {
                    padding-top: 80px;
                }

                /* Navbar brand sizing handled in navbar.blade.php */

                .section-title {
                    font-size: 2.5rem;
                }

                .hero-section h1 {
                    font-size: 3rem !important;
                }

                /* Footer Medium Device Styles */
                .footer {
                    padding: 50px 0 25px;
                }

                .footer-legal-links {
                    justify-content: flex-end;
                }
            }

            /* Large devices (desktops, 992px and up) */
            @media (min-width: 992px) and (max-width: 1199.98px) {
                .container {
                    max-width: 960px;
                }

                .hero-section h1 {
                    font-size: 3.5rem !important;
                }
            }

            /* Extra large devices (large desktops, 1200px and up) */
            @media (min-width: 1200px) and (max-width: 1399.98px) {
                .container {
                    max-width: 1140px;
                }
            }

            /* XXL devices (larger desktops, 1400px and up) - 14inch laptops */
            @media (min-width: 1400px) {
                .container {
                    max-width: 1320px;
                }

                .hero-section {
                    min-height: 85vh;
                }

                .section-title {
                    font-size: 3.5rem;
                }

                .hero-section h1 {
                    font-size: 4rem !important;
                }
            }

            /* Mobile-First Specific Styles */

            /* Hero Section Mobile Improvements */
            .hero-content {
                text-align: center;
            }

            /* Collections Mobile Improvements */
            @media (max-width: 576px) {
                .section-title {
                    font-size: 2rem;
                    margin-bottom: 1rem;
                }

                .product-card .card-img-top {
                    height: 200px;
                }

                .product-card .card-body {
                    padding: 1rem 0.75rem;
                }

                .product-card .card-title {
                    font-size: 1.1rem;
                    margin-bottom: 0.5rem;
                }

                .product-card .card-text {
                    font-size: 0.85rem;
                    margin-bottom: 0.75rem;
                }

                .btn-lg {
                    padding: 0.75rem 1.5rem;
                    font-size: 1rem;
                }

                /* Featured Products Mobile Spacing */
                .py-5 {
                    padding-top: 2rem !important;
                    padding-bottom: 2rem !important;
                }
            }

            /* Small tablets and large phones */
            @media (min-width: 576px) and (max-width: 768px) {
                .product-card .card-img-top {
                    height: 220px;
                }
            }

            /* Better mobile product overlay */
            @media (max-width: 768px) {
                .product-overlay {
                    opacity: 1;
                    background: rgba(0, 0, 0, 0.3);
                }

                .product-overlay .btn {
                    font-size: 0.85rem;
                    padding: 0.5rem 1rem;
                }
            }

            @media (min-width: 992px) {
                .hero-content {
                    text-align: left;
                }
            }

            .hero-main-image {
                max-height: 400px;
                object-fit: cover;
                width: 100%;
            }

            @media (min-width: 768px) {
                .hero-main-image {
                    max-height: 500px;
                }
            }

            @media (min-width: 1200px) {
                .hero-main-image {
                    max-height: 600px;
                }
            }

            /* Floating Cards */
            .floating-card .card {
                width: 120px;
                animation: float 3s ease-in-out infinite;
            }

            .floating-card-1 {
                animation-delay: 0s;
            }

            .floating-card-2 {
                animation-delay: 1.5s;
            }

            @keyframes float {
                0%, 100% { transform: translateY(0px) translateX(-50%); }
                50% { transform: translateY(-10px) translateX(-50%); }
            }

            /* Stats Section */
            .stats-section {
                margin-top: 2rem;
            }

            @media (min-width: 992px) {
                .stats-section {
                    margin-top: 3rem;
                }
            }

            .stat-number {
                font-size: 1.5rem;
            }

            @media (min-width: 576px) {
                .stat-number {
                    font-size: 2rem;
                }
            }

            @media (min-width: 992px) {
                .stat-number {
                    font-size: 2.5rem;
                }
            }

            .stat-label {
                font-size: 0.8rem;
                display: block;
                margin-top: 0.25rem;
            }

            @media (min-width: 576px) {
                .stat-label {
                    font-size: 0.9rem;
                }
            }

            /* Testimonials Mobile Improvements */
            .testimonial-card {
                transition: all 0.3s ease;
            }

            .testimonial-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
            }

            .testimonial-text {
                font-size: 0.95rem;
                line-height: 1.6;
            }

            @media (min-width: 768px) {
                .testimonial-text {
                    font-size: 1rem;
                }
            }

            .customer-avatar {
                width: 45px !important;
                height: 45px !important;
            }

            @media (min-width: 768px) {
                .customer-avatar {
                    width: 50px !important;
                    height: 50px !important;
                }
            }

            .customer-name {
                font-size: 0.9rem;
            }

            .customer-location {
                font-size: 0.8rem;
            }

            /* ===== ENHANCED RESPONSIVE DESIGN ===== */

            /* Mobile-First Touch Improvements */
            @media (max-width: 575.98px) {
                .btn {
                    min-height: 48px;
                    padding: 14px 20px;
                    font-size: 0.9rem;
                }

                .form-control,
                .form-select {
                    min-height: 48px;
                    font-size: 16px; /* Prevents zoom on iOS */
                }

                .card-body {
                    padding: 1rem;
                }

                .nav-link {
                    min-height: 48px;
                    padding: 14px 16px;
                }
            }

            /* Tablet Touch Improvements */
            @media (min-width: 576px) and (max-width: 991.98px) {
                .btn {
                    min-height: 44px;
                    padding: 12px 24px;
                }

                .card-body {
                    padding: 1.25rem;
                }

                /* Improve tap targets */
                .nav-link,
                .dropdown-item {
                    min-height: 44px;
                    display: flex;
                    align-items: center;
                }

                .form-control,
                .form-select {
                    min-height: 44px;
                }
            }

            /* Desktop Enhancements */
            @media (min-width: 992px) {
                .container {
                    max-width: 1200px;
                }
            }

            /* Loading and Performance */
            .hero-main-image,
            .card-img-top {
                loading: lazy;
            }

            /* Accessibility improvements */
            @media (prefers-reduced-motion: reduce) {
                .floating-card .card,
                .card:hover,
                .testimonial-card:hover,
                .product-card:hover {
                    animation: none;
                    transform: none;
                }
            }
        </style>

        @stack('styles')
    </head>
    <body>
        <!-- Navigation -->
        @include('layouts.navbar')

        <!-- Main Content -->
        <main class="main-content">
            @yield('content')
        </main>

        <!-- Footer -->
        @include('layouts.footer')



        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
        
        <!-- Custom JavaScript -->
        <script>

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        const navbarHeight = document.querySelector('.navbar').offsetHeight;
                        const targetPosition = target.offsetTop - navbarHeight;
                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Cart and Wishlist functionality
            function updateCartCount(count = null) {
                if (count !== null) {
                    // Update with provided count
                    const cartBadges = document.querySelectorAll('.cart-count');
                    cartBadges.forEach(badge => {
                        badge.textContent = count;
                    });
                } else {
                    // Fetch current count from API
                    fetch('{{ route("api.cart.count") }}')
                        .then(response => response.json())
                        .then(data => {
                            const cartBadges = document.querySelectorAll('.cart-count');
                            cartBadges.forEach(badge => {
                                badge.textContent = data.count;
                            });
                        })
                        .catch(error => {
                            console.error('Error updating cart count:', error);
                        });
                }
            }



            // Initialize counts on page load
            document.addEventListener('DOMContentLoaded', function() {
                updateCartCount();
            });
        </script>

        @stack('scripts')
    </body>
</html>
