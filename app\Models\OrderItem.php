<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'product_id',
        'product_name',
        'product_sku',
        'price',
        'quantity',
        'size_id',
        'size_name',
        'size_old', // For migration purposes
        'product_options',
        'total',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'total' => 'decimal:2',
        'product_options' => 'array',
    ];

    // Relationships
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function size()
    {
        return $this->belongsTo(Size::class);
    }

    /**
     * Get the size display name
     */
    public function getSizeDisplayNameAttribute()
    {
        // Use stored size_name for historical accuracy
        if ($this->size_name) {
            return $this->size_name;
        }

        // Fallback to current size relationship
        if ($this->size) {
            return $this->size->full_display_name;
        }

        // Fallback to old size field during migration
        return $this->size_old;
    }
}
