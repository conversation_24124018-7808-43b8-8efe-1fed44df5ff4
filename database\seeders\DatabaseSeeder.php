<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            CategorySeeder::class,
            ProductSeeder::class,
            PageSeeder::class,
        ]);

        // Create admin user
        \App\Models\User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'status' => 'active',
            'membership_level' => 'vip',
        ]);

        // Create sample customer
        \App\Models\User::create([
            'name' => '<PERSON><PERSON> Sharma',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '+ 91 9753447832',
            'role' => 'customer',
            'status' => 'active',
            'membership_level' => 'vip',
            'total_spent' => 245000,
            'reward_points' => 2450,
        ]);
    }
}
