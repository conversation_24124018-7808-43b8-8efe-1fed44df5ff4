<?php

namespace Database\Seeders;

use App\Models\Size;
use Illuminate\Database\Seeder;

class SizeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sizes = [
            // Clothing sizes
            ['name' => 'XS', 'display_name' => 'Extra Small', 'category_type' => 'clothing', 'sort_order' => 1],
            ['name' => 'S', 'display_name' => 'Small', 'category_type' => 'clothing', 'sort_order' => 2],
            ['name' => 'M', 'display_name' => 'Medium', 'category_type' => 'clothing', 'sort_order' => 3],
            ['name' => 'L', 'display_name' => 'Large', 'category_type' => 'clothing', 'sort_order' => 4],
            ['name' => 'XL', 'display_name' => 'Extra Large', 'category_type' => 'clothing', 'sort_order' => 5],
            ['name' => 'XXL', 'display_name' => '2X Large', 'category_type' => 'clothing', 'sort_order' => 6],
            ['name' => '3XL', 'display_name' => '3X Large', 'category_type' => 'clothing', 'sort_order' => 7],
            
            // Ring sizes (US standard)
            ['name' => '4', 'display_name' => 'Size 4', 'category_type' => 'rings', 'sort_order' => 1],
            ['name' => '4.5', 'display_name' => 'Size 4.5', 'category_type' => 'rings', 'sort_order' => 2],
            ['name' => '5', 'display_name' => 'Size 5', 'category_type' => 'rings', 'sort_order' => 3],
            ['name' => '5.5', 'display_name' => 'Size 5.5', 'category_type' => 'rings', 'sort_order' => 4],
            ['name' => '6', 'display_name' => 'Size 6', 'category_type' => 'rings', 'sort_order' => 5],
            ['name' => '6.5', 'display_name' => 'Size 6.5', 'category_type' => 'rings', 'sort_order' => 6],
            ['name' => '7', 'display_name' => 'Size 7', 'category_type' => 'rings', 'sort_order' => 7],
            ['name' => '7.5', 'display_name' => 'Size 7.5', 'category_type' => 'rings', 'sort_order' => 8],
            ['name' => '8', 'display_name' => 'Size 8', 'category_type' => 'rings', 'sort_order' => 9],
            ['name' => '8.5', 'display_name' => 'Size 8.5', 'category_type' => 'rings', 'sort_order' => 10],
            ['name' => '9', 'display_name' => 'Size 9', 'category_type' => 'rings', 'sort_order' => 11],
            ['name' => '9.5', 'display_name' => 'Size 9.5', 'category_type' => 'rings', 'sort_order' => 12],
            ['name' => '10', 'display_name' => 'Size 10', 'category_type' => 'rings', 'sort_order' => 13],
            ['name' => '10.5', 'display_name' => 'Size 10.5', 'category_type' => 'rings', 'sort_order' => 14],
            ['name' => '11', 'display_name' => 'Size 11', 'category_type' => 'rings', 'sort_order' => 15],
            ['name' => '11.5', 'display_name' => 'Size 11.5', 'category_type' => 'rings', 'sort_order' => 16],
            ['name' => '12', 'display_name' => 'Size 12', 'category_type' => 'rings', 'sort_order' => 17],
            
            // Shoe sizes (US standard)
            ['name' => '5', 'display_name' => 'Size 5', 'category_type' => 'shoes', 'sort_order' => 1],
            ['name' => '5.5', 'display_name' => 'Size 5.5', 'category_type' => 'shoes', 'sort_order' => 2],
            ['name' => '6', 'display_name' => 'Size 6', 'category_type' => 'shoes', 'sort_order' => 3],
            ['name' => '6.5', 'display_name' => 'Size 6.5', 'category_type' => 'shoes', 'sort_order' => 4],
            ['name' => '7', 'display_name' => 'Size 7', 'category_type' => 'shoes', 'sort_order' => 5],
            ['name' => '7.5', 'display_name' => 'Size 7.5', 'category_type' => 'shoes', 'sort_order' => 6],
            ['name' => '8', 'display_name' => 'Size 8', 'category_type' => 'shoes', 'sort_order' => 7],
            ['name' => '8.5', 'display_name' => 'Size 8.5', 'category_type' => 'shoes', 'sort_order' => 8],
            ['name' => '9', 'display_name' => 'Size 9', 'category_type' => 'shoes', 'sort_order' => 9],
            ['name' => '9.5', 'display_name' => 'Size 9.5', 'category_type' => 'shoes', 'sort_order' => 10],
            ['name' => '10', 'display_name' => 'Size 10', 'category_type' => 'shoes', 'sort_order' => 11],
            ['name' => '10.5', 'display_name' => 'Size 10.5', 'category_type' => 'shoes', 'sort_order' => 12],
            ['name' => '11', 'display_name' => 'Size 11', 'category_type' => 'shoes', 'sort_order' => 13],
            ['name' => '11.5', 'display_name' => 'Size 11.5', 'category_type' => 'shoes', 'sort_order' => 14],
            ['name' => '12', 'display_name' => 'Size 12', 'category_type' => 'shoes', 'sort_order' => 15],
            
            // General/One size
            ['name' => 'OS', 'display_name' => 'One Size', 'category_type' => 'general', 'sort_order' => 1],
            ['name' => 'Free', 'display_name' => 'Free Size', 'category_type' => 'general', 'sort_order' => 2],
            
            // Jewelry sizes (for bracelets, necklaces)
            ['name' => '16"', 'display_name' => '16 inches', 'category_type' => 'jewelry', 'sort_order' => 1],
            ['name' => '18"', 'display_name' => '18 inches', 'category_type' => 'jewelry', 'sort_order' => 2],
            ['name' => '20"', 'display_name' => '20 inches', 'category_type' => 'jewelry', 'sort_order' => 3],
            ['name' => '22"', 'display_name' => '22 inches', 'category_type' => 'jewelry', 'sort_order' => 4],
            ['name' => '24"', 'display_name' => '24 inches', 'category_type' => 'jewelry', 'sort_order' => 5],
        ];

        foreach ($sizes as $size) {
            Size::firstOrCreate(
                ['name' => $size['name'], 'category_type' => $size['category_type']],
                $size
            );
        }
    }
}
