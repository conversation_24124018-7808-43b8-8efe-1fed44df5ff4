<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Page extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'excerpt',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'template',
        'is_published',
        'show_in_menu',
        'menu_order',
        'featured_image',
        'seo_data',
        'created_by',
        'updated_by',
        'published_at',
    ];

    protected $casts = [
        'meta_keywords' => 'array',
        'seo_data' => 'array',
        'is_published' => 'boolean',
        'show_in_menu' => 'boolean',
        'published_at' => 'datetime',
    ];

    protected $dates = [
        'published_at',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($page) {
            if (empty($page->slug)) {
                $page->slug = Str::slug($page->title);
            }

            if (empty($page->meta_title)) {
                $page->meta_title = $page->title;
            }

            if (empty($page->published_at) && $page->is_published) {
                $page->published_at = now();
            }
        });

        static::updating(function ($page) {
            if ($page->isDirty('title') && empty($page->slug)) {
                $page->slug = Str::slug($page->title);
            }

            if ($page->isDirty('is_published') && $page->is_published && empty($page->published_at)) {
                $page->published_at = now();
            }
        });
    }

    /**
     * Get the user who created this page.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this page.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope to get only published pages.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true)
                    ->where(function ($q) {
                        $q->whereNull('published_at')
                          ->orWhere('published_at', '<=', now());
                    });
    }

    /**
     * Scope to get pages for menu.
     */
    public function scopeForMenu($query)
    {
        return $query->where('show_in_menu', true)
                    ->orderBy('menu_order')
                    ->orderBy('title');
    }

    /**
     * Scope to get pages for footer.
     */
    public function scopeForFooter($query)
    {
        return $query->whereIn('template', ['legal', 'policy', 'guide'])
                    ->orderBy('title');
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Get the URL for this page.
     */
    public function getUrlAttribute()
    {
        return route('page.show', $this->slug);
    }

    /**
     * Get the excerpt or generate one from content.
     */
    public function getExcerptAttribute($value)
    {
        if (!empty($value)) {
            return $value;
        }

        // Generate excerpt from content
        $content = strip_tags($this->content);
        return Str::limit($content, 160);
    }

    /**
     * Get the meta description or generate one.
     */
    public function getMetaDescriptionAttribute($value)
    {
        if (!empty($value)) {
            return $value;
        }

        return $this->excerpt;
    }

    /**
     * Get the template view name.
     */
    public function getTemplateViewAttribute()
    {
        return "pages.templates.{$this->template}";
    }

    /**
     * Check if page has a custom template.
     */
    public function hasCustomTemplate()
    {
        return $this->template !== 'default';
    }
}
