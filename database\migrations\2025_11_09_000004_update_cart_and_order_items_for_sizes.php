<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update cart_items table
        Schema::table('cart_items', function (Blueprint $table) {
            // Check if columns don't already exist before adding them
            if (!Schema::hasColumn('cart_items', 'size_id')) {
                $table->foreignId('size_id')->nullable()->after('product_id')->constrained()->onDelete('cascade');
            }
            if (!Schema::hasColumn('cart_items', 'size_old')) {
                $table->string('size_old')->nullable()->after('size_id');
            }
        });

        // Copy existing size data to size_old column for cart_items (only if size column exists)
        if (Schema::hasColumn('cart_items', 'size')) {
            DB::statement('UPDATE cart_items SET size_old = size WHERE size IS NOT NULL');

            // Drop the old size column from cart_items
            Schema::table('cart_items', function (Blueprint $table) {
                $table->dropColumn('size');
            });
        }

        // Update order_items table
        Schema::table('order_items', function (Blueprint $table) {
            // Check if columns don't already exist before adding them
            if (!Schema::hasColumn('order_items', 'size_id')) {
                $table->foreignId('size_id')->nullable()->after('product_id')->constrained()->onDelete('cascade');
            }
            if (!Schema::hasColumn('order_items', 'size_name')) {
                $table->string('size_name')->nullable()->after('size_id'); // Store size name for historical records
            }
            if (!Schema::hasColumn('order_items', 'size_old')) {
                $table->string('size_old')->nullable()->after('size_name');
            }
        });

        // Copy existing size data to size_old column for order_items (only if size column exists)
        if (Schema::hasColumn('order_items', 'size')) {
            DB::statement('UPDATE order_items SET size_old = size, size_name = size WHERE size IS NOT NULL');

            // Drop the old size column from order_items
            Schema::table('order_items', function (Blueprint $table) {
                $table->dropColumn('size');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore cart_items table
        Schema::table('cart_items', function (Blueprint $table) {
            $table->string('size')->nullable();
        });

        // Copy data back from size_old to size
        DB::statement('UPDATE cart_items SET size = size_old WHERE size_old IS NOT NULL');

        Schema::table('cart_items', function (Blueprint $table) {
            $table->dropForeign(['size_id']);
            $table->dropColumn(['size_id', 'size_old']);
        });

        // Restore order_items table
        Schema::table('order_items', function (Blueprint $table) {
            $table->string('size')->nullable();
        });

        // Copy data back from size_old to size
        DB::statement('UPDATE order_items SET size = size_old WHERE size_old IS NOT NULL');

        Schema::table('order_items', function (Blueprint $table) {
            $table->dropForeign(['size_id']);
            $table->dropColumn(['size_id', 'size_name', 'size_old']);
        });
    }
};
