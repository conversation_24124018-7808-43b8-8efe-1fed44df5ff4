<?php

/**
 * Test Script for Hierarchical Category-Size System
 * 
 * This script tests the new hierarchical category and size management system
 * Run this script after running the migrations to verify everything works correctly.
 * 
 * Usage: php test_hierarchical_system.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Category;
use App\Models\Size;
use App\Models\Product;
use App\Models\ProductSize;

// Initialize Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🧪 Testing Hierarchical Category-Size System\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Test 1: Category Hierarchy
echo "📁 Test 1: Category Hierarchy\n";
echo "-" . str_repeat("-", 30) . "\n";

try {
    // Create root category
    $rootCategory = Category::create([
        'name' => 'Test Jewelry',
        'slug' => 'test-jewelry',
        'description' => 'Test root category for jewelry',
        'is_active' => true,
        'sort_order' => 1
    ]);
    
    // Create child category
    $childCategory = Category::create([
        'name' => 'Test Rings',
        'slug' => 'test-rings',
        'description' => 'Test child category for rings',
        'parent_id' => $rootCategory->id,
        'is_active' => true,
        'sort_order' => 1
    ]);
    
    // Update hierarchy
    $childCategory->updateLevel();
    $childCategory->updatePath();
    
    echo "✅ Root category created: {$rootCategory->name} (ID: {$rootCategory->id})\n";
    echo "✅ Child category created: {$childCategory->name} (ID: {$childCategory->id})\n";
    echo "✅ Child category level: {$childCategory->level}\n";
    echo "✅ Child category path: {$childCategory->path}\n";
    
    // Test hierarchy methods
    $descendants = $rootCategory->getDescendants();
    echo "✅ Root category has {$descendants->count()} descendants\n";
    
    $breadcrumb = $childCategory->getBreadcrumb();
    echo "✅ Child category breadcrumb: " . $breadcrumb->pluck('name')->implode(' > ') . "\n";
    
} catch (Exception $e) {
    echo "❌ Category hierarchy test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Size Management
echo "📏 Test 2: Size Management\n";
echo "-" . str_repeat("-", 30) . "\n";

try {
    // Get some sizes
    $ringSizes = Size::where('category_type', 'rings')->take(3)->get();
    $generalSizes = Size::where('category_type', 'general')->take(2)->get();
    
    echo "✅ Found {$ringSizes->count()} ring sizes\n";
    echo "✅ Found {$generalSizes->count()} general sizes\n";
    
    // Attach sizes to categories
    if ($ringSizes->count() > 0) {
        $childCategory->sizes()->attach($ringSizes->pluck('id'));
        echo "✅ Attached ring sizes to child category\n";
    }
    
    if ($generalSizes->count() > 0) {
        $rootCategory->sizes()->attach($generalSizes->pluck('id'));
        echo "✅ Attached general sizes to root category\n";
    }
    
    // Test size inheritance
    $availableSizes = $childCategory->getAvailableSizes();
    echo "✅ Child category has {$availableSizes->count()} available sizes\n";
    
} catch (Exception $e) {
    echo "❌ Size management test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Product-Size-Stock Management
echo "📦 Test 3: Product-Size-Stock Management\n";
echo "-" . str_repeat("-", 30) . "\n";

try {
    // Create a test product
    $product = Product::create([
        'name' => 'Test Ring Product',
        'slug' => 'test-ring-product',
        'description' => 'A test ring product for testing the new system',
        'short_description' => 'Test ring',
        'price' => 1000.00,
        'sku' => 'TEST-RING-001',
        'category_id' => $childCategory->id,
        'stock_quantity' => 100,
        'is_active' => true,
        'in_stock' => true
    ]);
    
    echo "✅ Test product created: {$product->name} (ID: {$product->id})\n";
    
    // Create ProductSize records
    $availableSizes = $childCategory->getAvailableSizes();
    foreach ($availableSizes->take(3) as $size) {
        ProductSize::create([
            'product_id' => $product->id,
            'size_id' => $size->id,
            'stock_quantity' => rand(5, 20),
            'price_adjustment' => rand(0, 100),
            'is_available' => true
        ]);
    }
    
    echo "✅ Created ProductSize records for {$availableSizes->take(3)->count()} sizes\n";
    
    // Test product size methods
    $productSizes = $product->getAvailableSizesWithStock();
    echo "✅ Product has {$productSizes->count()} available sizes with stock info\n";
    
    // Test stock status
    $product->updateOverallStockStatus();
    echo "✅ Product overall stock status updated\n";
    
} catch (Exception $e) {
    echo "❌ Product-size-stock test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: API Endpoints
echo "🌐 Test 4: API Endpoints\n";
echo "-" . str_repeat("-", 30) . "\n";

try {
    // Test category hierarchy API
    $response = file_get_contents(url('/admin/api/categories/hierarchy'));
    $hierarchyData = json_decode($response, true);
    
    if ($hierarchyData && isset($hierarchyData['success']) && $hierarchyData['success']) {
        echo "✅ Category hierarchy API working\n";
    } else {
        echo "❌ Category hierarchy API failed\n";
    }
    
    // Test category sizes API
    $response = file_get_contents(url("/admin/api/categories/{$childCategory->id}/sizes"));
    $sizesData = json_decode($response, true);
    
    if ($sizesData && isset($sizesData['success']) && $sizesData['success']) {
        echo "✅ Category sizes API working\n";
        echo "✅ Category has {$sizesData['sizes_count']} sizes available\n";
    } else {
        echo "❌ Category sizes API failed\n";
    }
    
} catch (Exception $e) {
    echo "❌ API endpoints test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: Data Migration Verification
echo "🔄 Test 5: Data Migration Verification\n";
echo "-" . str_repeat("-", 30) . "\n";

try {
    // Check if sizes table has data
    $sizesCount = Size::count();
    echo "✅ Sizes table has {$sizesCount} records\n";
    
    // Check if product_sizes table has data
    $productSizesCount = ProductSize::count();
    echo "✅ ProductSizes table has {$productSizesCount} records\n";
    
    // Check if categories have hierarchy data
    $categoriesWithParents = Category::whereNotNull('parent_id')->count();
    echo "✅ {$categoriesWithParents} categories have parent relationships\n";
    
    // Check if any products have size relationships
    $productsWithSizes = Product::whereHas('productSizes')->count();
    echo "✅ {$productsWithSizes} products have size relationships\n";
    
} catch (Exception $e) {
    echo "❌ Data migration verification failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Cleanup Test Data
echo "🧹 Cleaning up test data...\n";
echo "-" . str_repeat("-", 30) . "\n";

try {
    // Delete test product and related data
    if (isset($product)) {
        ProductSize::where('product_id', $product->id)->delete();
        $product->delete();
        echo "✅ Test product and sizes deleted\n";
    }
    
    // Delete test categories
    if (isset($childCategory)) {
        $childCategory->sizes()->detach();
        $childCategory->delete();
        echo "✅ Test child category deleted\n";
    }
    
    if (isset($rootCategory)) {
        $rootCategory->sizes()->detach();
        $rootCategory->delete();
        echo "✅ Test root category deleted\n";
    }
    
} catch (Exception $e) {
    echo "❌ Cleanup failed: " . $e->getMessage() . "\n";
}

echo "\n";
echo "🎉 Testing completed!\n";
echo "=" . str_repeat("=", 50) . "\n";
echo "\nNext steps:\n";
echo "1. Run 'php artisan migrate' to apply all migrations\n";
echo "2. Check the admin interface for category and product management\n";
echo "3. Test the frontend product pages for size selection\n";
echo "4. Verify cart and order functionality with the new size system\n";
