@extends('layouts.admin')

@section('title', 'Announcements')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row align-items-center mb-4">
        <div class="col-12 col-md-6">
            <h1 class="h4 mb-1 text-gray-800">Updates & Offers</h1>
            <p class="text-muted mb-0">Manage announcements, updates, and promotional offers</p>
        </div>
        <div class="col-12 col-md-6 mt-2 mt-md-0">
            <div class="d-flex justify-content-md-end">
                <a href="{{ route('admin.announcements.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create Announcement
                </a>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    @endif

    <!-- Announcements Table -->
    <div class="card shadow">
        <div class="card-body">
            @if($announcements->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Title</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Dates</th>
                                <th>Sort</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($announcements as $announcement)
                            <tr>
                                <td>
                                    <img src="{{ $announcement->image_url }}" 
                                         alt="{{ $announcement->title }}" 
                                         class="rounded" 
                                         style="width: 60px; height: 60px; object-fit: cover;">
                                </td>
                                <td>
                                    <div>
                                        <h6 class="mb-1">{{ $announcement->title }}</h6>
                                        <small class="text-muted">{{ Str::limit($announcement->description, 60) }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $announcement->type === 'offer' ? 'danger' : ($announcement->type === 'update' ? 'primary' : 'success') }}">
                                        {{ ucfirst($announcement->type) }}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-toggle-status {{ $announcement->is_active ? 'btn-success' : 'btn-secondary' }}" 
                                            data-id="{{ $announcement->id }}">
                                        <i class="fas fa-{{ $announcement->is_active ? 'check' : 'times' }}"></i>
                                        {{ $announcement->is_active ? 'Active' : 'Inactive' }}
                                    </button>
                                </td>
                                <td>
                                    <small class="d-block">Created: {{ $announcement->created_at->format('M d, Y') }}</small>
                                    @if($announcement->expires_at)
                                        <small class="text-danger">Expires: {{ $announcement->expires_at->format('M d, Y') }}</small>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ $announcement->sort_order }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.announcements.edit', $announcement->id) }}" 
                                           class="btn btn-sm btn-outline-primary" 
                                           title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-danger btn-delete" 
                                                data-id="{{ $announcement->id }}"
                                                title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-3">
                    {{ $announcements->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No announcements found</h5>
                    <p class="text-muted">Create your first announcement to display updates and offers on the homepage.</p>
                    <a href="{{ route('admin.announcements.create') }}" class="btn btn-primary mt-2">
                        <i class="fas fa-plus me-2"></i>Create Announcement
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle Status
    document.querySelectorAll('.btn-toggle-status').forEach(btn => {
        btn.addEventListener('click', function() {
            const id = this.dataset.id;
            
            fetch(`/admin/announcements/${id}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            });
        });
    });

    // Delete Announcement
    document.querySelectorAll('.btn-delete').forEach(btn => {
        btn.addEventListener('click', function() {
            const id = this.dataset.id;

            if (confirm('Are you sure you want to delete this announcement?')) {
                fetch(`/admin/announcements/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    }
                });
            }
        });
    });
});
</script>
@endpush

