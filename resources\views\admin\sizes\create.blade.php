@extends('layouts.admin')

@section('title', 'Create Size')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Create New Size</h1>
                    <p class="text-muted">Add a new size for product categories</p>
                </div>
                <a href="{{ route('admin.sizes.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Sizes
                </a>
            </div>

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Please fix the following errors:</strong>
                    <ul class="mb-0 mt-2">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="card">
                <div class="card-body">
                    <form id="createSizeForm" method="POST" action="{{ route('admin.sizes.store') }}">
                        @csrf

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Size Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required
                                       placeholder="e.g., XS, 6, 32">
                                <div class="form-text">Short identifier for the size (e.g., XS, 6, 32)</div>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="display_name" class="form-label">Display Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('display_name') is-invalid @enderror" 
                                       id="display_name" name="display_name" value="{{ old('display_name') }}" required
                                       placeholder="e.g., Extra Small, Size 6, 32 inches">
                                <div class="form-text">Full name shown to customers</div>
                                @error('display_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="category_type" class="form-label">Category Type <span class="text-danger">*</span></label>
                                <select class="form-select @error('category_type') is-invalid @enderror" 
                                        id="category_type" name="category_type" required>
                                    <option value="">Select Category Type</option>
                                    <option value="clothing" {{ old('category_type') === 'clothing' ? 'selected' : '' }}>Clothing</option>
                                    <option value="shoes" {{ old('category_type') === 'shoes' ? 'selected' : '' }}>Shoes</option>
                                    <option value="rings" {{ old('category_type') === 'rings' ? 'selected' : '' }}>Rings</option>
                                    <option value="jewelry" {{ old('category_type') === 'jewelry' ? 'selected' : '' }}>Jewelry</option>
                                    <option value="general" {{ old('category_type') === 'general' ? 'selected' : '' }}>General</option>
                                </select>
                                <div class="form-text">Type of products this size applies to</div>
                                @error('category_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0"
                                       placeholder="0">
                                <div class="form-text">Lower numbers appear first (0 = first)</div>
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Optional description or notes about this size">{{ old('description') }}</textarea>
                            <div class="form-text">Optional additional information about this size</div>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                       {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active
                                </label>
                                <div class="form-text">Only active sizes can be assigned to categories and products</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.sizes.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save me-2"></i>Create Size
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createSizeForm');
    const submitBtn = document.getElementById('submitBtn');

    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
    });

    // Auto-generate display name from name if not manually changed
    const nameInput = document.getElementById('name');
    const displayNameInput = document.getElementById('display_name');
    let displayNameManuallyChanged = false;

    displayNameInput.addEventListener('input', function() {
        displayNameManuallyChanged = true;
    });

    nameInput.addEventListener('input', function() {
        if (!displayNameManuallyChanged && this.value) {
            const categoryType = document.getElementById('category_type').value;
            let displayName = this.value;
            
            // Auto-generate display name based on category type
            if (categoryType === 'clothing') {
                if (['XS', 'S', 'M', 'L', 'XL', 'XXL'].includes(this.value.toUpperCase())) {
                    const sizeMap = {
                        'XS': 'Extra Small',
                        'S': 'Small',
                        'M': 'Medium',
                        'L': 'Large',
                        'XL': 'Extra Large',
                        'XXL': 'Double Extra Large'
                    };
                    displayName = sizeMap[this.value.toUpperCase()] || this.value;
                }
            } else if (categoryType === 'shoes') {
                displayName = `Size ${this.value}`;
            } else if (categoryType === 'jewelry') {
                displayName = `${this.value} inches`;
            }
            
            displayNameInput.value = displayName;
        }
    });

    // Update display name when category type changes
    document.getElementById('category_type').addEventListener('change', function() {
        if (!displayNameManuallyChanged && nameInput.value) {
            nameInput.dispatchEvent(new Event('input'));
        }
    });
});
</script>
@endpush
