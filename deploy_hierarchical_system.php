<?php

/**
 * Deployment Script for Hierarchical Category-Size System
 * 
 * This script helps deploy the new hierarchical category and size management system
 * It runs migrations in the correct order and provides feedback on the process.
 * 
 * Usage: php deploy_hierarchical_system.php
 */

echo "🚀 Deploying Hierarchical Category-Size System\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Check if we're in the Laravel root directory
if (!file_exists('artisan')) {
    echo "❌ Error: This script must be run from the Laravel root directory\n";
    exit(1);
}

// Step 1: Backup database (optional but recommended)
echo "💾 Step 1: Database Backup (Recommended)\n";
echo "-" . str_repeat("-", 40) . "\n";
echo "⚠️  It's highly recommended to backup your database before proceeding.\n";
echo "   You can do this through your hosting panel or using mysqldump.\n";
echo "\n";

// Step 2: Run migrations
echo "🔄 Step 2: Running Migrations\n";
echo "-" . str_repeat("-", 40) . "\n";

$migrations = [
    '2025_11_09_000001_add_hierarchy_to_categories_table.php',
    '2025_11_09_000002_create_sizes_table.php',
    '2025_11_09_000003_create_product_sizes_table.php',
    '2025_11_09_000004_update_cart_and_order_items_for_sizes.php',
    '2025_11_09_000005_create_category_sizes_table.php',
    '2025_11_09_000006_migrate_existing_product_sizes.php'
];

echo "The following migrations will be run:\n";
foreach ($migrations as $migration) {
    echo "  • {$migration}\n";
}
echo "\n";

// Ask for confirmation
echo "Do you want to proceed with the migrations? (y/N): ";
$handle = fopen("php://stdin", "r");
$confirmation = trim(fgets($handle));
fclose($handle);

if (strtolower($confirmation) !== 'y' && strtolower($confirmation) !== 'yes') {
    echo "❌ Deployment cancelled by user.\n";
    exit(0);
}

echo "\n🔄 Running migrations...\n";

// Run the migrations
$output = [];
$returnCode = 0;
exec('php artisan migrate --force 2>&1', $output, $returnCode);

if ($returnCode === 0) {
    echo "✅ Migrations completed successfully!\n";
    foreach ($output as $line) {
        echo "   {$line}\n";
    }
} else {
    echo "❌ Migration failed!\n";
    foreach ($output as $line) {
        echo "   {$line}\n";
    }
    echo "\nPlease check the error messages above and fix any issues.\n";
    exit(1);
}

echo "\n";

// Step 3: Clear caches
echo "🧹 Step 3: Clearing Caches\n";
echo "-" . str_repeat("-", 40) . "\n";

$cacheCommands = [
    'config:clear' => 'Configuration cache',
    'route:clear' => 'Route cache',
    'view:clear' => 'View cache',
    'cache:clear' => 'Application cache'
];

foreach ($cacheCommands as $command => $description) {
    echo "Clearing {$description}...\n";
    $output = [];
    $returnCode = 0;
    exec("php artisan {$command} 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ {$description} cleared\n";
    } else {
        echo "⚠️  Warning: Failed to clear {$description}\n";
    }
}

echo "\n";

// Step 4: Verify installation
echo "✅ Step 4: Verification\n";
echo "-" . str_repeat("-", 40) . "\n";

// Check if tables exist
$tables = [
    'sizes' => 'Sizes table',
    'product_sizes' => 'Product sizes table',
    'category_sizes' => 'Category sizes table'
];

foreach ($tables as $table => $description) {
    $output = [];
    $returnCode = 0;
    exec("php artisan tinker --execute=\"echo \\Illuminate\\Support\\Facades\\Schema::hasTable('{$table}') ? 'exists' : 'missing';\" 2>&1", $output, $returnCode);
    
    $result = end($output);
    if (strpos($result, 'exists') !== false) {
        echo "✅ {$description} exists\n";
    } else {
        echo "❌ {$description} missing\n";
    }
}

// Check if Size model works
echo "\nTesting Size model...\n";
$output = [];
$returnCode = 0;
exec("php artisan tinker --execute=\"echo \\App\\Models\\Size::count() . ' sizes found';\" 2>&1", $output, $returnCode);

$result = end($output);
if (strpos($result, 'sizes found') !== false) {
    echo "✅ {$result}\n";
} else {
    echo "❌ Size model test failed\n";
}

echo "\n";

// Step 5: Post-deployment instructions
echo "📋 Step 5: Post-Deployment Instructions\n";
echo "-" . str_repeat("-", 40) . "\n";

echo "✅ Deployment completed successfully!\n\n";

echo "Next steps to complete the setup:\n\n";

echo "1. 🔧 Admin Interface:\n";
echo "   • Log into your admin panel\n";
echo "   • Go to Categories section\n";
echo "   • Edit existing categories to set up hierarchy and sizes\n";
echo "   • Create new categories with parent-child relationships\n\n";

echo "2. 📦 Product Management:\n";
echo "   • Edit existing products to configure size-stock matrix\n";
echo "   • Create new products with size-specific stock and pricing\n";
echo "   • Verify that size selection works correctly\n\n";

echo "3. 🛒 Frontend Testing:\n";
echo "   • Visit product detail pages\n";
echo "   • Test size selection and stock status display\n";
echo "   • Test add to cart functionality with sizes\n";
echo "   • Complete a test order to verify the entire flow\n\n";

echo "4. 🔍 Category Navigation:\n";
echo "   • Check category pages for hierarchical breadcrumbs\n";
echo "   • Verify subcategory navigation works\n";
echo "   • Test category filtering and product display\n\n";

echo "5. 📊 Data Verification:\n";
echo "   • Run: php test_hierarchical_system.php\n";
echo "   • Check that existing data was migrated correctly\n";
echo "   • Verify that old size data is preserved\n\n";

echo "🎉 Your hierarchical category-size system is now ready!\n";
echo "\nFor support or issues, check the Laravel logs and ensure all\n";
echo "model relationships are working correctly.\n";

echo "\n" . str_repeat("=", 50) . "\n";
