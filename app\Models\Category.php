<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $parent_id
 * @property string $name
 * @property string $slug
 * @property string $description
 * @property string $image
 * @property array $available_sizes
 * @property bool $inherit_parent_sizes
 * @property int $level
 * @property string $path
 * @property bool $is_active
 * @property bool $is_featured
 * @property int $sort_order
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 *
 * @property-read \App\Models\Category $parent
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Category[] $children
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Product[] $products
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Size[] $sizes
 *
 * @method static \Illuminate\Database\Eloquent\Builder active()
 * @method static \Illuminate\Database\Eloquent\Builder featured()
 * @method static \Illuminate\Database\Eloquent\Builder ordered()
 * @method static \Illuminate\Database\Eloquent\Builder roots()
 * @method static \Illuminate\Database\Eloquent\Builder withLevel(int $level)
 */
class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'parent_id',
        'name',
        'slug',
        'description',
        'image',
        'available_sizes',
        'inherit_parent_sizes',
        'level',
        'path',
        'is_active',
        'is_featured',
        'sort_order',
    ];

    protected $casts = [
        'available_sizes' => 'array',
        'inherit_parent_sizes' => 'boolean',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
    ];

    // Relationships
    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id')->ordered();
    }

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function sizes()
    {
        return $this->belongsToMany(Size::class, 'category_sizes')->withTimestamps();
    }

    public function allProducts()
    {
        // Get products from this category and all descendant categories
        $categoryIds = $this->getDescendants()->pluck('id')->push($this->id);
        return Product::whereIn('category_id', $categoryIds);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    public function scopeRoots($query)
    {
        return $query->whereNull('parent_id');
    }

    public function scopeWithLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    // Helper methods
    public function isRoot()
    {
        return is_null($this->parent_id);
    }

    public function hasChildren()
    {
        return $this->children()->exists();
    }

    public function getAncestors()
    {
        $ancestors = collect();
        $current = $this->parent;

        while ($current) {
            $ancestors->prepend($current);
            $current = $current->parent;
        }

        return $ancestors;
    }

    public function getDescendants()
    {
        $descendants = collect();

        foreach ($this->children as $child) {
            $descendants->push($child);
            $descendants = $descendants->merge($child->getDescendants());
        }

        return $descendants;
    }

    public function getBreadcrumb()
    {
        $breadcrumb = $this->getAncestors();
        $breadcrumb->push($this);

        return $breadcrumb;
    }

    /**
     * Get all available sizes for this category
     * Considers inheritance from parent categories
     */
    public function getAvailableSizes()
    {
        if (!$this->inherit_parent_sizes || $this->isRoot()) {
            // Use only this category's sizes from the relationship
            return $this->sizes()->active()->ordered()->get();
        }

        // Inherit from parent and merge with own sizes
        $parentSizes = $this->parent ? $this->parent->getAvailableSizes() : collect();
        $ownSizes = $this->sizes()->active()->ordered()->get();

        // Merge and remove duplicates
        return $parentSizes->merge($ownSizes)->unique('id')->sortBy('sort_order');
    }

    /**
     * Update the category path for efficient hierarchy queries
     */
    public function updatePath()
    {
        $path = $this->getAncestors()->pluck('id')->implode('/');
        if ($path) {
            $path .= '/' . $this->id;
        } else {
            $path = (string) $this->id;
        }

        $this->update(['path' => $path]);

        // Update children paths
        foreach ($this->children as $child) {
            $child->updatePath();
        }
    }

    /**
     * Update the category level
     */
    public function updateLevel()
    {
        $level = $this->getAncestors()->count();
        $this->update(['level' => $level]);

        // Update children levels
        foreach ($this->children as $child) {
            $child->updateLevel();
        }
    }

    /**
     * Get category type for size filtering
     */
    public function getCategoryTypeAttribute()
    {
        // Determine category type based on name or parent
        $name = strtolower($this->name);

        if (str_contains($name, 'ring')) {
            return 'rings';
        } elseif (str_contains($name, 'clothing') || str_contains($name, 'shirt') || str_contains($name, 'dress')) {
            return 'clothing';
        } elseif (str_contains($name, 'shoe') || str_contains($name, 'footwear')) {
            return 'shoes';
        }

        // Check parent category type
        if ($this->parent) {
            return $this->parent->category_type;
        }

        return 'general';
    }

    // Accessors
    public function getImageUrlAttribute()
    {
        if (!$this->image) {
            return 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80';
        }

        // Check if it's already a full URL
        if (filter_var($this->image, FILTER_VALIDATE_URL)) {
            return $this->image;
        }

        // Clean the path and ensure storage/ prefix
        $cleanPath = str_replace(['\\/', '\\'], '/', $this->image);
        $cleanPath = ltrim($cleanPath, '/');
        if (!str_starts_with($cleanPath, 'storage/')) {
            $cleanPath = 'storage/' . $cleanPath;
        }

        // Return local storage URL
        return asset($cleanPath);
    }

    // Helper methods
    public function getRouteKeyName()
    {
        return 'slug';
    }
}
