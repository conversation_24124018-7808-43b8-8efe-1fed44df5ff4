<!-- ====== Modern Bootstrap Footer ====== -->
<footer class="bg-dark text-light pt-5 pb-3">
  <div class="container">
    <div class="row gy-4">

      <!-- Brand Section -->
      <div class="col-md-4 text-center text-md-start">
<div class="d-flex text-aligent-center align-items-center ">        <img src="{{ asset('logo-footer.png') }}" alt="Kanha Fashion Hub" class="rounded-2 mb-3 border border-1 border-white" width="70" height="70">
        <h5 class="fw-bold ms-2 text-info">Kanha Fashion Hub</h5></div>
        <hr class="text-white p-0 m-0">
        <p class="small text-secondary">
          Crafting exquisite fashion with passion and precision. Each piece tells a story of timeless elegance.
        </p>
        <div class="d-flex justify-content-center justify-content-md-start gap-2">
          @foreach($contact['social'] as $platform => $details)
          <a href="{{ $details['url'] }}" target="_blank" class="social-icon">
            <i class="fab fa-{{ $platform === 'facebook' ? 'facebook-f' : $platform }}"></i>
          </a>
          @endforeach
        </div>
      </div>

      <!-- Quick Links -->
      <div class="col-md-2">
        <h6 class="fw-bold text-uppercase mb-3">Quick Links</h6>
        <ul class="list-unstyled small">
          <li><a href="{{ url('/') }}" class="footer-link">Home</a></li>
          <li><a href="{{ url('/collections') }}" class="footer-link">Collections</a></li>
           <li><a href="{{ url('/track-order') }}" class="footer-link">Track Order</a></li>
          @if(isset($menuPages) && $menuPages->count() > 0)
            @foreach($menuPages->take(4) as $page)
              <li><a href="{{ route('page.show', $page->slug) }}" class="footer-link">{{ $page->title }}</a></li>
            @endforeach
          @else
            <li><a href="{{ url('/about') }}" class="footer-link">About-Us</a></li>
            <li><a href="{{ url('/contact') }}" class="footer-link">Contact-Us</a></li>
           
          @endif
        </ul>
      </div>

      <!-- Categories -->
      <div class="col-md-3">
        <h6 class="fw-bold text-uppercase mb-3">Categories</h6>
        <ul class="list-unstyled small">
          @if(isset($globalCategories) && $globalCategories->count() > 0)
            @foreach($globalCategories->take(7) as $category)
              <li><a href="{{ route('collections.category', $category->slug) }}" class="footer-link">{{ $category->name }}</a></li>
            @endforeach
          @else
            <li><a href="{{ url('/collections') }}" class="footer-link">All Collections</a></li>
          @endif
        </ul>
      </div>

      <!-- Contact Info -->
      <div class="col-md-3">
        <h6 class="fw-bold text-uppercase mb-3">Contact Info</h6>
        <ul class="list-unstyled small">
          <li><i class="fas fa-map-marker-alt me-2 text-warning"></i>{{ $contact['address'] }}</li>
          <li><i class="fas fa-phone me-2 text-warning"></i><a href="tel:{{ $contact['phoneLink'] }}" class="footer-link">{{ $contact['phoneFormatted'] }}</a></li>
          <li><i class="fas fa-envelope me-2 text-warning"></i><a href="mailto:{{ $contact['email'] }}" class="footer-link">{{ $contact['email'] }}</a></li>
          <li><i class="fab fa-whatsapp me-2 text-warning"></i><a href="{{ $contact['whatsappLink'] }}" target="_blank" class="footer-link">{{ $contact['whatsappLink'] }}</a></li>
        </ul>
      </div>
    </div>

    <!-- Bottom Footer -->
    <hr class="border-secondary my-4">
    <div class="d-flex flex-column flex-md-row justify-content-between align-items-center text-center small">
      <div>
        @if(isset($footerPages) && $footerPages->count() > 0)
          @foreach($footerPages as $page)
            <a href="{{ route('page.show', $page->slug) }}" class="footer-link mx-2">{{ $page->title }}</a>
          @endforeach
        @else
          <a href="{{ route('privacy-policy') }}" class="footer-link mx-2">Privacy Policy</a>
          <a href="{{ route('terms-of-service') }}" class="footer-link mx-2">Terms</a>
          <a href="{{ route('cookie-policy') }}" class="footer-link mx-2">Cookies</a>
        @endif
      </div>
    </div>
  </div>

</footer>
  <div style="background-color: #000000; text-align: center; padding: 15px;">
            <p class="mb-0 text-info">&copy; {{ date('Y') }} Kanha Fashion Hub. All Rights Reserved. </p>
<hr class="text-white m-0 p-0">
  <p style="color: #FFFFFF; margin: 0;">
    Designed & Developed By 
    <a class="text-decoration-none fw-bold" href="https://marutiitzone.com/" style="color: #ff4800;">Maruti IT Zone</a>
  </p>
</div>

<!-- Scroll to Top Button -->
<button id="scrollTopBtn" class="btn btn-pink rounded-circle shadow position-fixed">
  <i class="fas fa-arrow-up"></i>
</button>

<!-- ====== Styles ====== -->
<style>
.footer-link {
  color: #bbb;
  text-decoration: none;
  transition: color 0.3s;
}
.footer-link:hover {
  color: #ffeb3b; /* Yellow */
}
.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #ff4081; /* Pink */
  color: #fff;
  transition: all 0.3s ease;
}
.social-icon:hover {
  background: #ffeb3b; /* Yellow */
  color: #000;
}
#scrollTopBtn {
  bottom: 30px;
  right: 30px;
  width: 45px;
  height: 45px;
  background-color: #ff4081;
  color: #fff;
  border: none;
  display: none;
  z-index: 999;
}
#scrollTopBtn:hover {
  background-color: #ffeb3b;
  color: #000;
}
</style>

<!-- ====== Script ====== -->
<script>
const scrollBtn = document.getElementById("scrollTopBtn");
window.addEventListener("scroll", () => {
  scrollBtn.style.display = window.scrollY > 300 ? "flex" : "none";
});
scrollBtn.addEventListener("click", () => {
  window.scrollTo({ top: 0, behavior: "smooth" });
});
</script>
